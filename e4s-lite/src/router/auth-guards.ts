import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

export interface RouteMetaAuth {
  requiresAuth?: boolean
  roles?: string[]
  permissions?: string[]
  redirectTo?: string
}

declare module 'vue-router' {
  interface RouteMeta extends RouteMetaAuth {}
}

/**
 * Authentication guard that checks if user is authenticated
 * and has required roles/permissions
 */
export const authGuard = async (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
): Promise<void> => {
  const authStore = useAuthStore()
  
  // Check if route requires authentication
  if (!to.meta?.requiresAuth) {
    next()
    return
  }

  // Initialize auth store if not already done
  if (!authStore.isAuthenticated && authStore.token) {
    await authStore.initialize()
  }

  // Check if user is authenticated
  if (!authStore.isAuthenticated) {
    // Store the intended destination
    const returnUrl = to.fullPath
    next({
      name: 'login',
      query: { returnUrl }
    })
    return
  }

  // Check role requirements
  if (to.meta.roles && to.meta.roles.length > 0) {
    const userRole = authStore.userRole
    if (!userRole || !to.meta.roles.includes(userRole)) {
      next({
        name: 'unauthorized',
        query: { message: 'Insufficient permissions' }
      })
      return
    }
  }

  // Check permission requirements
  if (to.meta.permissions && to.meta.permissions.length > 0) {
    const hasRequiredPermission = to.meta.permissions.some(permission =>
      authStore.hasPermission(permission)
    )
    
    if (!hasRequiredPermission) {
      next({
        name: 'unauthorized',
        query: { message: 'Insufficient permissions' }
      })
      return
    }
  }

  // User is authenticated and authorized
  next()
}

/**
 * Guest guard that redirects authenticated users away from guest-only pages
 */
export const guestGuard = (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
): void => {
  const authStore = useAuthStore()
  
  if (authStore.isAuthenticated) {
    // Redirect to return URL or home
    const returnUrl = to.query.returnUrl as string
    if (returnUrl) {
      next(returnUrl)
    } else {
      next({ name: 'home' })
    }
    return
  }
  
  next()
}

/**
 * Helper function to check if a route requires authentication
 */
export const requiresAuth = (route: RouteLocationNormalized): boolean => {
  return route.meta?.requiresAuth === true
}

/**
 * Helper function to check if user has required role for a route
 */
export const hasRequiredRole = (route: RouteLocationNormalized, userRole: string | null): boolean => {
  if (!route.meta?.roles || route.meta.roles.length === 0) {
    return true
  }
  
  return userRole ? route.meta.roles.includes(userRole) : false
}

/**
 * Helper function to check if user has required permissions for a route
 */
export const hasRequiredPermissions = (
  route: RouteLocationNormalized,
  userPermissions: string[]
): boolean => {
  if (!route.meta?.permissions || route.meta.permissions.length === 0) {
    return true
  }
  
  return route.meta.permissions.some(permission =>
    userPermissions.includes(permission)
  )
}
