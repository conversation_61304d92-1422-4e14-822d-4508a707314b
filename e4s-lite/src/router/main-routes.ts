import ResultsWrapper from '@/views/ResultsWrapper.vue'

export const mainRoutes = [
  {
    path: '/results/:id',
    name: 'results',
    component: ResultsWrapper
  },
  {
    path: '/secure-results/:id',
    name: 'secure-results',
    component: ResultsWrapper,
    meta: {
      requiresAuth: true,
      roles: ['USER', 'E4SUSER', 'APPADMIN']
    }
  },
  {
    path: '/admin-results/:id',
    name: 'admin-results',
    component: ResultsWrapper,
    meta: {
      requiresAuth: true,
      roles: ['E4SUSER', 'APPADMIN']
    }
  }
]
