import { createRouter, createWebHashHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
// import ResultsWrapper from '@/views/ResultsWrapper.vue'
import AboutView from '@/views/AboutView.vue'
import MainLaunch from '@/components/MainLaunch.vue'
import { mainRoutes } from '@/router/main-routes'
import { authGuard, guestGuard } from '@/router/auth-guards'

// history: createWebHistory(import.meta.env.BASE_URL),

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: MainLaunch,
      children: mainRoutes
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue')
    },
    {
      path: '/results-em/:id',
      name: 'results-em',
      component: () => import('../views/ResultsWrapperEmbedded.vue')
    },
    {
      path: '/admin',
      name: 'server-admin',
      component: () => import('../views/AdminDashboardView.vue'),
      meta: {
        requiresAuth: true,
        roles: ['APPADMIN', 'E4SUSER']
      }
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../components/results/auth/LoginPage.vue'),
      beforeEnter: guestGuard
    },
    {
      path: '/unauthorized',
      name: 'unauthorized',
      component: () => import('../components/results/auth/UnauthorizedPage.vue')
    },
    {
      path: '/auth-success',
      name: 'auth-success',
      component: () => import('../components/results/auth/AuthSuccess.vue')
    },
    {
      path: '/auth-test',
      name: 'auth-test',
      component: () => import('../components/results/auth/AuthSuccessTest.vue')
    }
  ]
})

// Global navigation guard
router.beforeEach(authGuard)

export default router
