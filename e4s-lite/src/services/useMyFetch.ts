import { createFetch } from '@vueuse/core'

const getAuthToken = (): string | null => {
  return localStorage.getItem('auth_token')
}

export const useMyFetch = createFetch({
  baseUrl: import.meta.env.VITE_APP_API_HOST,
  options: {
    async beforeFetch({ options }) {
      const token = getAuthToken()

      if (token) {
        options.headers = {
          ...options.headers,
          Authorization: `Bearer ${token}`
        }
      }

      return { options }
    },
    afterFetch(ctx) {
      // console.log('Fetched typeof:' + typeof ctx.data, ctx.data)
      ctx.data = JSON.parse(ctx.data)
      return ctx
    },
    onFetchError(ctx) {
      // Handle 401 Unauthorized responses
      if (ctx.response?.status === 401) {
        // Clear invalid token
        localStorage.removeItem('auth_token')
        // Redirect to login if not already there
        if (!window.location.hash.includes('/login')) {
          window.location.hash = '/login'
        }
      }
      return ctx
    }
  },
  fetchOptions: {
    mode: 'cors'
  }
})
