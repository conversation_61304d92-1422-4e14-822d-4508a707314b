# Authentication System

This project implements a comprehensive authentication system with JWT tokens, route guards, and role-based access control.

## Features

- **JWT Token Authentication**: Secure token-based authentication
- **Route Guards**: Automatic redirection for protected routes
- **Role-Based Access Control**: Different access levels for different user roles
- **Automatic Token Handling**: JWT tokens are automatically included in all API requests
- **OAuth Integration**: Google OAuth login support
- **Persistent Sessions**: Tokens are stored in localStorage for persistent sessions

## Usage

### Protecting Routes

To protect a route, add the `requiresAuth` meta property:

```typescript
{
  path: '/admin',
  name: 'admin',
  component: AdminComponent,
  meta: {
    requiresAuth: true,
    roles: ['APPADMIN', 'E4SUSER'], // Optional: restrict to specific roles
    permissions: ['ADMIN_ACCESS'] // Optional: restrict to specific permissions
  }
}
```

### Using Authentication in Components

Use the `useAuth` composable for easy access to authentication state:

```vue
<script setup lang="ts">
import { useAuth } from '@/composables/useAuth'

const auth = useAuth()

// Check if user is authenticated
if (auth.isAuthenticated.value) {
  // User is logged in
}

// Check user role
if (auth.hasRole('APPADMIN')) {
  // User is an admin
}

// Check permissions
if (auth.hasPermission('ADMIN_ACCESS')) {
  // User has admin access
}
</script>

<template>
  <div v-if="auth.isAuthenticated.value">
    Welcome, {{ auth.user.value?.name }}!
    <button @click="auth.logout()">Logout</button>
  </div>
  <div v-else>
    <button @click="auth.login()">Login</button>
  </div>
</template>
```

### Available User Roles

- `ANON`: Anonymous user (not logged in)
- `USER`: Regular authenticated user
- `E4SUSER`: Entry4Sports user with elevated privileges
- `APPADMIN`: Application administrator with full access

### Route Examples

```typescript
// Public route - no authentication required
{
  path: '/results/:id',
  name: 'results',
  component: ResultsWrapper
}

// Protected route - authentication required
{
  path: '/secure-results/:id',
  name: 'secure-results',
  component: ResultsWrapper,
  meta: {
    requiresAuth: true
  }
}

// Admin only route
{
  path: '/admin',
  name: 'admin',
  component: AdminDashboard,
  meta: {
    requiresAuth: true,
    roles: ['APPADMIN', 'E4SUSER']
  }
}
```

### API Integration

The authentication system automatically:

1. **Includes JWT tokens** in all API requests via the `Authorization: Bearer <token>` header
2. **Handles 401 responses** by clearing invalid tokens and redirecting to login
3. **Manages token lifecycle** including storage and cleanup

### Login Flow

1. User clicks "Login" button
2. User is redirected to Google OAuth with success callback URL
3. After successful authentication, user is redirected to `/auth-success?token=<jwt>&redirect=<destination>`
4. AuthSuccess page extracts token and redirect parameters from URL
5. Token is stored and verified with the server
6. User is redirected to their intended destination or home page
7. URL is cleaned to remove sensitive token information

### Logout Flow

1. User clicks "Logout" button
2. Token is removed from localStorage
3. User state is cleared
4. User is redirected to home page

### Error Handling

The system handles various error scenarios:

- **Invalid tokens**: Automatically cleared and user redirected to login
- **Expired tokens**: Detected on API calls and handled gracefully
- **Network errors**: Proper error messages displayed to user
- **Unauthorized access**: Users redirected to unauthorized page

### Security Features

- **Automatic token cleanup** on logout or invalid tokens
- **Route protection** prevents access to protected pages
- **Role-based access control** for fine-grained permissions
- **Secure token storage** in localStorage with automatic cleanup
- **CSRF protection** through JWT tokens

### Auth Success Page

The `/auth-success` page handles OAuth callback with URL parameters:

- **URL Format**: `/auth-success?token=<jwt_token>&redirect=<destination_url>`
- **Token Parameter**: JWT token from OAuth provider
- **Redirect Parameter**: Where to redirect user after successful authentication
- **Security**: Automatically cleans sensitive parameters from URL
- **Error Handling**: Provides retry options and clear error messages

Example usage:
```
/auth-success?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&redirect=/admin
```

## Files Structure

- `src/stores/auth.ts` - Pinia store for authentication state
- `src/router/auth-guards.ts` - Route guards for authentication
- `src/composables/useAuth.ts` - Composable for easy auth access
- `src/components/results/auth/LoginPage.vue` - Login page component
- `src/components/results/auth/UnauthorizedPage.vue` - Unauthorized access page
- `src/components/horse/AuthSuccess.vue` - OAuth callback success page
- `src/services/useMyFetch.ts` - HTTP client with automatic token handling
