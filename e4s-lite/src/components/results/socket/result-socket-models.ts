export type EaLevel = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 0

export const RESULT_SOCKET_ACTION_TYPES = [
  'results-manual',
  'field-results',
  'photofinish',
  'cache-updated',
  'athlete-bibno',
  'field-nextup',
  'track-move',
  'track-movevert',
  'track-movehorz',
  'track-movehoriz',
  'entries-present',
  'seed-dropped',
  'seed-confirmed',
  'entries-checkin',
  'entry-cancel'
] as const

export type ResultSocketActionType = (typeof RESULT_SOCKET_ACTION_TYPES)[number]

export interface SocketResultData<Payload> {
  action: ResultSocketActionType
  domain: string
  comp: SocketResultCompSummary
  payload: Payload
  key: string
  deviceKey: string
  utcTime: string
}

export interface SocketListenConfig {
  listenForMessageDomains: string[]
  listenForCompIds: number[]
  listenForMessageTypes: ResultSocketActionType[]
}

export interface SocketShouldMessageBeAcceptedResult {
  identifier:
    | 'OK'
    | 'NO_COMP'
    | 'NO_COMP_ID_MATCH'
    | 'MESSAGE_TYPE_NO_MATCH'
    | 'DOMAIN_NO_MATCH'
    | 'EVENT_GROUP_NO_MATCH'
  should: boolean
  reason: string
  level: 1 | 2 | 3 | 4 | 5 | 100
}

export interface SocketResultCompSummary {
  id: number
  name: string
}

export interface SocketResultEventGroup {
  id: number
  name: string
  eventNo: number
}
