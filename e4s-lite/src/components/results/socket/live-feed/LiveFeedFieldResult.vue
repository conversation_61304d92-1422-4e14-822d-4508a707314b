<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <div class="e4s-flex-row e4s-justify-flex-space-between">
      <div class="e4s-flex-row e4s-gap--standard e4s-header--500">
        <div v-text="payload.eventGroup.name"></div>
      </div>

      <div class="e4s-flex-row e4s-header--500 e4s-flex-row--end">
        <div
          v-text="payload.latest.currentTrial.trialNo + ': '"
          class="live-feed-field-result--top-right-label"
        ></div>
        <div
          v-text="payload.latest.currentTrial.score"
          class="live-feed-field-result--top-right-value"
          style="text-align: right"
        ></div>
      </div>
    </div>

    <div
      class="e4s-flex-row e4s-justify-flex-space-between e4s-subheader--general e4s-subheader--300"
    >
      <div class="e4s-flex-row e4s-gap--standard">
        <div v-text="'#' + payload.latest.bibNo"></div>
        <div v-text="getAthleteName"></div>
      </div>

      <div class="e4s-flex-row e4s-flex-row--end">
        <div class="live-feed-field-result--top-right-label">Pos:</div>
        <div
          v-text="payload.latest.position"
          class="e4s-flex-column live-feed-field-result--top-right-value"
          style="text-align: right"
        ></div>
      </div>
    </div>

    <!--    <div class="e4s-flex-row e4s-flex-row&#45;&#45;end" v-if="hasTrial">-->
    <!--      <Trials :trails="payload.latest.trial" />-->
    <!--    </div>-->

    <!--    <LiveFeedHeatAthletes :athletes="payload.heatSummary" />-->
  </div>
</template>
<script setup lang="ts">
import { computed, type PropType } from 'vue'
import type {
  LiveFeedMessageBase,
  LiveFeedMessageFieldResult
} from '@/components/results/socket/live-feed/live-feed-models'
import Trials from '@/components/Trials.vue'
import LiveFeedHeatAthletes from '@/components/results/socket/live-feed/LiveFeedHeatAthletes.vue'

const props = defineProps({
  message: {
    type: Object as PropType<LiveFeedMessageBase>,
    required: true
  }
})

const payload = computed<LiveFeedMessageFieldResult>(() => {
  return props.message.payload as LiveFeedMessageFieldResult
})

const getAthleteName = computed(() => {
  if (!payload.value.latest.athleteResult) {
    return ''
  }

  const athlete = payload.value.latest.athleteResult.athlete
  return athlete.firstName + ' ' + athlete.surName
})

const hasTrial = computed(() => {
  if (!payload.value.latest.trial) {
    return false
  }
  return Object.keys(payload.value.latest.trial).length > 0
})
</script>

<style scoped>
.live-feed-field-result--top-right-label {
  width: 50px;
}
.live-feed-field-result--top-right-value {
  width: 50px;
}
</style>
