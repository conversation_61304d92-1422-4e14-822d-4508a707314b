/*
{
    "key": "s-c3183456-2e84-4f48-8813-0dc9979de643",
    "comp": {
        "id": 617
    },
    "action": "track-move",
    "deviceKey": "",
    "securityKey": "",
    "payload": {
        "checkedIn": false,
        "isTeam": false,
        "entryA": {
            "entryId": 228090,
            "heatNo": 3,
            "laneNo": 5,
            "present": true
        },
        "entryB": {
            "entryId": 228077,
            "heatNo": 2,
            "laneNo": 5
        }
    },
    "domain": "dev25.entry4sports.co.uk",
    "utcTime": "2025-06-13T08:27:45.985Z"
}
 */

import type { AthleteIdString, EntryIdString } from '@/services/common'
import type { SocketResultMessagePayloadTrackMove } from '@/components/results/socket/track-move/models/track-move'
import type { CompEvent, IndividualEntry } from '@/components/results/store-comp-results'
import { simpleClone } from '@/services/common-service-utils'

export function updateTrackMove(
  payload: SocketResultMessagePayloadTrackMove,
  compEvent: CompEvent
): CompEvent {
  const compEventInternal = simpleClone(compEvent)

  if (!compEventInternal.entries) {
    return compEventInternal
  }

  // create a map by entryId
  const entryMap: Record<EntryIdString, AthleteIdString> = {}
  Object.values(compEventInternal.entries).forEach((entry: IndividualEntry) => {
    entryMap[entry.entryId.toString()] = entry.athleteId.toString()
  })

  const entryId_A = payload.entryA.entryId.toString()
  const entrId_B = payload.entryB.entryId.toString()

  const athleteId_A = entryMap[entryId_A]
  const athleteId_B = entryMap[entrId_B]

  //  Get the entries
  const entryA = compEventInternal.entries[athleteId_A]
  const entryB = compEventInternal.entries[athleteId_B]

  // is entryB.entryId > 0
  const isEntryBValid = payload.entryB.entryId > 0

  //  If either entry is not found, exit
  if (!entryA || (!entryB && isEntryBValid)) {
    return compEventInternal
  }

  //  Update the seeding
  // entryA.seeding.heatNo = payload.entryA.heatNo
  // entryA.seeding.laneNo = payload.entryA.laneNo
  compEventInternal.entries[athleteId_A].seeding.heatNo = payload.entryA.heatNo
  compEventInternal.entries[athleteId_A].seeding.laneNo = payload.entryA.laneNo

  // entryB.seeding.heatNo = payload.entryB.heatNo
  if (isEntryBValid) {
    compEventInternal.entries[athleteId_B].seeding.heatNo = payload.entryB.heatNo
    compEventInternal.entries[athleteId_B].seeding.laneNo = payload.entryB.laneNo
  }

  // return {
  //   athleteId_A: athleteId_A,
  //   athleteId_B: athleteId_B,
  //   entryASeedingOld: compEvent.entries![athleteId_A].seeding,
  //   entryBSeedingOld: compEvent.entries![athleteId_B].seeding,
  //   entryASeedingNew: compEventInternal.entries[athleteId_A].seeding,
  //   entryBSeedingNew: compEventInternal.entries[athleteId_B].seeding
  // }
  return compEventInternal
}
