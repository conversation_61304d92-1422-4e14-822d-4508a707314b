/**
 * {
 *     "key": "s-584bd976-4d14-4b85-be7c-8eae407203c8",
 *     "comp": {
 *         "id": 617
 *     },
 *     "action": "track-movehoriz",
 *     "deviceKey": "",
 *     "securityKey": "",
 *     "payload": {
 *         "checkedIn": false,
 *         "factor": -1,
 *         "entryInfo": [
 *             {
 *                 "athleteId": 192707,
 *                 "entryId": 221930,
 *                 "teamId": 0
 *             },
 *             {
 *                 "athleteId": 113981,
 *                 "entryId": 222366,
 *                 "teamId": 0
 *             },
 *             {
 *                 "athleteId": 187224,
 *                 "entryId": 224433,
 *                 "teamId": 0
 *             },
 *             {
 *                 "athleteId": 194227,
 *                 "entryId": 225042,
 *                 "teamId": 0
 *             },
 *             {
 *                 "athleteId": 192851,
 *                 "entryId": 223275,
 *                 "teamId": 0
 *             },
 *             {
 *                 "athleteId": 187527,
 *                 "entryId": 224710,
 *                 "teamId": 0
 *             },
 *             {
 *                 "athleteId": 194251,
 *                 "entryId": 225034,
 *                 "teamId": 0
 *             }
 *         ]
 *     },
 *     "domain": "dev25.entry4sports.co.uk",
 *     "utcTime": "2025-07-07T03:23:13.673Z"
 * }
 */

export interface SocketResultMessagePayloadTrackMoveHorz {
  checkedIn: boolean
  factor: number
  entryInfo: SocketResultMessagePayloadTrackMoveHorzEntry[]
}

export interface SocketResultMessagePayloadTrackMoveHorzEntry {
  athleteId: number
  entryId: number
  teamId: number
}
