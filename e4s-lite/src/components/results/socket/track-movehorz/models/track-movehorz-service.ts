import type { CompEvent, IndividualEntry } from '@/components/results/store-comp-results'
import type { SocketResultMessagePayloadTrackMoveHorz } from '@/components/results/socket/track-movehorz/models/track-movehorz'
import { simpleClone } from '@/services/common-service-utils'
import type { AthleteIdString, EntryIdString } from '@/services/common'

/**
 * Updates athlete lane positions horizontally (left/right) within their current heat
 * @param compEvent The competition event to update
 * @param payload The track move horizontal payload containing factor and entry info
 * @returns Updated competition event with modified lane positions
 */
export function updateTrackMoveHorz(
  compEvent: CompEvent,
  payload: SocketResultMessagePayloadTrackMoveHorz
): CompEvent {
  const compEventInternal = simpleClone(compEvent)

  if (!compEventInternal.entries || !payload.entryInfo || payload.entryInfo.length === 0) {
    return compEventInternal
  }

  // Create a map by entryId for quick lookup
  const entryMap: Record<EntryIdString, AthleteIdString> = {}
  Object.values(compEventInternal.entries).forEach((entry: IndividualEntry) => {
    entryMap[entry.entryId.toString()] = entry.athleteId.toString()
  })

  // Process each entry in the payload
  payload.entryInfo.forEach((entryInfo) => {
    const athleteIdString = entryMap[entryInfo.entryId.toString()]

    if (athleteIdString && compEventInternal.entries![athleteIdString]) {
      const entry = compEventInternal.entries![athleteIdString]

      // Calculate new lane number
      const currentLaneNo = entry.seeding.laneNo
      const newLaneNo = currentLaneNo + payload.factor

      // Only update if the new lane number is valid (positive)
      if (newLaneNo > 0) {
        entry.seeding.laneNo = newLaneNo

        // Also update checked-in lane number if it exists and is greater than 0
        if (entry.laneNoCheckedIn > 0) {
          const newCheckedInLaneNo = entry.laneNoCheckedIn + payload.factor
          if (newCheckedInLaneNo > 0) {
            entry.laneNoCheckedIn = newCheckedInLaneNo
          }
        }
      }
    }
  })

  return compEventInternal
}
