import type { CompEvent } from '@/components/results/store-comp-results'
import { factoryCompetitionScheduleOptions } from '@/components/results/results-service'
import type { SocketResultMessagePayloadTrackMoveHorz } from '@/components/results/socket/track-movehorz/models/track-movehorz'

export const mockCompEventTrackMoveHorz: CompEvent = {
  egId: 14425,
  name: '100m Test Event',
  eventNo: 23,
  typeNo: 'T23',
  eventDate: '2024-07-12T12:58:00.000Z',
  egOptions: factoryCompetitionScheduleOptions(),
  entries: {
    '192707': {
      entryId: 221930,
      athleteId: 192707,
      URN: '3905744',
      firstName: 'John',
      surName: 'Doe',
      aoCode: 'EA',
      perf: '10.94',
      eventAgeGroup: 'Senior',
      ageGroup: 'Senior',
      bibNo: '1193',
      teamBibNo: '53',
      clubName: 'Test Club A',
      gender: 'M',
      seeding: { heatNo: 1, laneNo: 4 },
      present: 1,
      wind: '',
      collected: 1,
      checkedIn: 1,
      heatNoCheckedIn: 1,
      laneNoCheckedIn: 4,
      classification: 0
    },
    '113981': {
      entryId: 222366,
      athleteId: 113981,
      URN: '3954315',
      firstName: 'Jane',
      surName: 'Smith',
      aoCode: 'EA',
      perf: '11.06',
      eventAgeGroup: 'Senior',
      ageGroup: 'Senior',
      bibNo: '1318',
      teamBibNo: '67',
      clubName: 'Test Club B',
      gender: 'F',
      seeding: { heatNo: 1, laneNo: 6 },
      present: 1,
      wind: '',
      collected: 1,
      checkedIn: 1,
      heatNoCheckedIn: 1,
      laneNoCheckedIn: 6,
      classification: 0
    },
    '187224': {
      entryId: 224433,
      athleteId: 187224,
      URN: '3968169',
      firstName: 'Bob',
      surName: 'Johnson',
      aoCode: 'EA',
      perf: '10.85',
      eventAgeGroup: 'Senior',
      ageGroup: 'Senior',
      bibNo: '1456',
      teamBibNo: '23',
      clubName: 'Test Club C',
      gender: 'M',
      seeding: { heatNo: 2, laneNo: 3 },
      present: 1,
      wind: '',
      collected: 1,
      checkedIn: 1,
      heatNoCheckedIn: 2,
      laneNoCheckedIn: 3,
      classification: 0
    },
    '194227': {
      entryId: 225042,
      athleteId: 194227,
      URN: '3994561',
      firstName: 'Alice',
      surName: 'Brown',
      aoCode: 'EA',
      perf: '11.25',
      eventAgeGroup: 'Senior',
      ageGroup: 'Senior',
      bibNo: '1789',
      teamBibNo: '45',
      clubName: 'Test Club D',
      gender: 'F',
      seeding: { heatNo: 2, laneNo: 1 },
      present: 1,
      wind: '',
      collected: 1,
      checkedIn: 0,
      heatNoCheckedIn: 0,
      laneNoCheckedIn: 0,
      classification: 0
    },
    '192851': {
      entryId: 223275,
      athleteId: 192851,
      URN: '3874154',
      firstName: 'Charlie',
      surName: 'Wilson',
      aoCode: 'EA',
      perf: '10.78',
      eventAgeGroup: 'Senior',
      ageGroup: 'Senior',
      bibNo: '1567',
      teamBibNo: '89',
      clubName: 'Test Club E',
      gender: 'M',
      seeding: { heatNo: 1, laneNo: 8 },
      present: 1,
      wind: '',
      collected: 1,
      checkedIn: 1,
      heatNoCheckedIn: 1,
      laneNoCheckedIn: 8,
      classification: 0
    },
    '187527': {
      entryId: 224710,
      athleteId: 187527,
      URN: '3968170',
      firstName: 'Diana',
      surName: 'Davis',
      aoCode: 'EA',
      perf: '11.45',
      eventAgeGroup: 'Senior',
      ageGroup: 'Senior',
      bibNo: '1890',
      teamBibNo: '12',
      clubName: 'Test Club F',
      gender: 'F',
      seeding: { heatNo: 2, laneNo: 5 },
      present: 1,
      wind: '',
      collected: 1,
      checkedIn: 1,
      heatNoCheckedIn: 2,
      laneNoCheckedIn: 5,
      classification: 0
    },
    '194251': {
      entryId: 225034,
      athleteId: 194251,
      URN: '3994562',
      firstName: 'Eve',
      surName: 'Miller',
      aoCode: 'EA',
      perf: '11.89',
      eventAgeGroup: 'Senior',
      ageGroup: 'Senior',
      bibNo: '1234',
      teamBibNo: '56',
      clubName: 'Test Club G',
      gender: 'F',
      seeding: { heatNo: 1, laneNo: 2 },
      present: 1,
      wind: '',
      collected: 1,
      checkedIn: 1,
      heatNoCheckedIn: 1,
      laneNoCheckedIn: 2,
      classification: 0
    }
  },
  results: {},
  teams: []
} as CompEvent

// Additional test payloads
export const mockTrackMoveHorzPayloadMoveRight: SocketResultMessagePayloadTrackMoveHorz = {
  checkedIn: false,
  factor: 2,
  entryInfo: [
    { athleteId: 192707, entryId: 221930, teamId: 0 },
    { athleteId: 113981, entryId: 222366, teamId: 0 },
    { athleteId: 187224, entryId: 224433, teamId: 0 }
  ]
}

export const mockTrackMoveHorzPayloadMoveLeft: SocketResultMessagePayloadTrackMoveHorz = {
  checkedIn: false,
  factor: -1,
  entryInfo: [
    { athleteId: 113981, entryId: 222366, teamId: 0 },
    { athleteId: 192851, entryId: 223275, teamId: 0 },
    { athleteId: 187527, entryId: 224710, teamId: 0 }
  ]
}

export const mockTrackMoveHorzPayloadInvalidMove: SocketResultMessagePayloadTrackMoveHorz = {
  checkedIn: false,
  factor: -5, // Would move athletes to negative lanes
  entryInfo: [
    { athleteId: 194227, entryId: 225042, teamId: 0 }, // Lane 1, would go to -4
    { athleteId: 194251, entryId: 225034, teamId: 0 }  // Lane 2, would go to -3
  ]
}
