import type { SocketResultMessagePayloadTrackMoveHorz } from '@/components/results/socket/track-movehorz/models/track-movehorz'

export const mockTrackMoveHorzPayload: SocketResultMessagePayloadTrackMoveHorz = {
  checkedIn: false,
  factor: -1,
  entryInfo: [
    {
      athleteId: 192707,
      entryId: 221930,
      teamId: 0
    },
    {
      athleteId: 113981,
      entryId: 222366,
      teamId: 0
    },
    {
      athleteId: 187224,
      entryId: 224433,
      teamId: 0
    },
    {
      athleteId: 194227,
      entryId: 225042,
      teamId: 0
    },
    {
      athleteId: 192851,
      entryId: 223275,
      teamId: 0
    },
    {
      athleteId: 187527,
      entryId: 224710,
      teamId: 0
    },
    {
      athleteId: 194251,
      entryId: 225034,
      teamId: 0
    }
  ]
}
