import { describe, it, expect } from 'vitest'
import type { SocketResultMessagePayloadTrackMoveHorz } from '@/components/results/socket/track-movehorz/models/track-movehorz'
import { updateTrackMoveHorz } from '@/components/results/socket/track-movehorz/models/track-movehorz-service'
import type { CompEvent } from '@/components/results/store-comp-results'
import { simpleClone } from '@/services/common-service-utils'
import {
  mockCompEventTrackMoveHorz,
  mockTrackMoveHorzPayloadMoveRight,
  mockTrackMoveHorzPayloadMoveLeft,
  mockTrackMoveHorzPayloadInvalidMove
} from '@/components/results/socket/track-movehorz/test/mocks/mock-comp-event-track-movehorz'

describe('updateTrackMoveHorz', () => {
  const createMockCompEvent = (): CompEvent => simpleClone(mockCompEventTrackMoveHorz)

  it('should move athletes to the right (positive factor)', () => {
    const compEvent = createMockCompEvent()

    const payload: SocketResultMessagePayloadTrackMoveHorz = {
      checkedIn: false,
      factor: 2, // Move 2 lanes to the right
      entryInfo: [
        { athleteId: 192707, entryId: 221930, teamId: 0 },
        { athleteId: 113981, entryId: 222366, teamId: 0 }
      ]
    }

    const result = updateTrackMoveHorz(compEvent, payload)

    // Check that athletes moved 2 lanes to the right
    expect(result.entries!['192707'].seeding.laneNo).toBe(6) // 4 + 2
    expect(result.entries!['192707'].laneNoCheckedIn).toBe(6) // 4 + 2

    expect(result.entries!['113981'].seeding.laneNo).toBe(8) // 6 + 2
    expect(result.entries!['113981'].laneNoCheckedIn).toBe(8) // 6 + 2

    // Check that other athletes were not affected
    expect(result.entries!['187224'].seeding.laneNo).toBe(3) // Unchanged
    expect(result.entries!['194227'].seeding.laneNo).toBe(1) // Unchanged
  })

  it('should move athletes to the left (negative factor)', () => {
    const compEvent = createMockCompEvent()

    const payload: SocketResultMessagePayloadTrackMoveHorz = {
      checkedIn: false,
      factor: -1, // Move 1 lane to the left
      entryInfo: [
        { athleteId: 113981, entryId: 222366, teamId: 0 },
        { athleteId: 187224, entryId: 224433, teamId: 0 }
      ]
    }

    const result = updateTrackMoveHorz(compEvent, payload)

    // Check that athletes moved 1 lane to the left
    expect(result.entries!['113981'].seeding.laneNo).toBe(5) // 6 - 1
    expect(result.entries!['113981'].laneNoCheckedIn).toBe(5) // 6 - 1

    expect(result.entries!['187224'].seeding.laneNo).toBe(2) // 3 - 1
    expect(result.entries!['187224'].laneNoCheckedIn).toBe(2) // 3 - 1

    // Check that other athletes were not affected
    expect(result.entries!['192707'].seeding.laneNo).toBe(4) // Unchanged
    expect(result.entries!['194227'].seeding.laneNo).toBe(1) // Unchanged
  })

  it('should not move athletes to invalid lane positions (lane 0 or negative)', () => {
    const compEvent = createMockCompEvent()

    const payload: SocketResultMessagePayloadTrackMoveHorz = {
      checkedIn: false,
      factor: -2, // Move 2 lanes to the left
      entryInfo: [
        { athleteId: 194227, entryId: 225042, teamId: 0 } // Currently in lane 1, would go to -1
      ]
    }

    const result = updateTrackMoveHorz(compEvent, payload)

    // Check that athlete stayed in original position (invalid move rejected)
    expect(result.entries!['194227'].seeding.laneNo).toBe(1) // Unchanged
    expect(result.entries!['194227'].laneNoCheckedIn).toBe(0) // Unchanged (was 0)
  })

  it('should handle athletes with no checked-in lane number', () => {
    const compEvent = createMockCompEvent()

    const payload: SocketResultMessagePayloadTrackMoveHorz = {
      checkedIn: false,
      factor: 1,
      entryInfo: [
        { athleteId: 194227, entryId: 225042, teamId: 0 } // Has laneNoCheckedIn = 0
      ]
    }

    const result = updateTrackMoveHorz(compEvent, payload)

    // Check that seeding lane moved but checked-in lane stayed 0
    expect(result.entries!['194227'].seeding.laneNo).toBe(2) // 1 + 1
    expect(result.entries!['194227'].laneNoCheckedIn).toBe(0) // Unchanged (was 0)
  })

  it('should handle empty entries', () => {
    const compEvent = createMockCompEvent()
    compEvent.entries = {}

    const payload: SocketResultMessagePayloadTrackMoveHorz = {
      checkedIn: false,
      factor: 1,
      entryInfo: [{ athleteId: 192707, entryId: 221930, teamId: 0 }]
    }

    const result = updateTrackMoveHorz(compEvent, payload)

    // Should return unchanged event
    expect(result.entries).toEqual({})
  })

  it('should handle missing entries property', () => {
    const compEvent = createMockCompEvent()
    delete compEvent.entries

    const payload: SocketResultMessagePayloadTrackMoveHorz = {
      checkedIn: false,
      factor: 1,
      entryInfo: [{ athleteId: 192707, entryId: 221930, teamId: 0 }]
    }

    const result = updateTrackMoveHorz(compEvent, payload)

    // Should return unchanged event
    expect(result.entries).toBeUndefined()
  })

  it('should handle empty entryInfo array', () => {
    const compEvent = createMockCompEvent()

    const payload: SocketResultMessagePayloadTrackMoveHorz = {
      checkedIn: false,
      factor: 1,
      entryInfo: []
    }

    const result = updateTrackMoveHorz(compEvent, payload)

    // Should return unchanged event
    expect(result.entries!['192707'].seeding.laneNo).toBe(4) // Unchanged
    expect(result.entries!['113981'].seeding.laneNo).toBe(6) // Unchanged
  })

  it('should handle non-existent entry IDs gracefully', () => {
    const compEvent = createMockCompEvent()

    const payload: SocketResultMessagePayloadTrackMoveHorz = {
      checkedIn: false,
      factor: 1,
      entryInfo: [
        { athleteId: 999999, entryId: 999999, teamId: 0 }, // Non-existent
        { athleteId: 192707, entryId: 221930, teamId: 0 } // Valid
      ]
    }

    const result = updateTrackMoveHorz(compEvent, payload)

    // Valid entry should be updated, invalid entry should be ignored
    expect(result.entries!['192707'].seeding.laneNo).toBe(5) // 4 + 1
    // expect(Object.keys(result.entries!)).toHaveLength(4) // No new entries added
  })

  it('should not modify the original compEvent object', () => {
    const compEvent = createMockCompEvent()
    const originalLaneNo = compEvent.entries!['192707'].seeding.laneNo

    const payload: SocketResultMessagePayloadTrackMoveHorz = {
      checkedIn: false,
      factor: 3,
      entryInfo: [{ athleteId: 192707, entryId: 221930, teamId: 0 }]
    }

    const result = updateTrackMoveHorz(compEvent, payload)

    // Original should be unchanged
    expect(compEvent.entries!['192707'].seeding.laneNo).toBe(originalLaneNo)
    // Result should be changed
    expect(result.entries!['192707'].seeding.laneNo).toBe(originalLaneNo + 3)
  })

  it('should handle zero factor (no movement)', () => {
    const compEvent = createMockCompEvent()

    const payload: SocketResultMessagePayloadTrackMoveHorz = {
      checkedIn: false,
      factor: 0, // No movement
      entryInfo: [
        { athleteId: 192707, entryId: 221930, teamId: 0 },
        { athleteId: 113981, entryId: 222366, teamId: 0 }
      ]
    }

    const result = updateTrackMoveHorz(compEvent, payload)

    // All athletes should remain in their original positions
    expect(result.entries!['192707'].seeding.laneNo).toBe(4) // Unchanged
    expect(result.entries!['113981'].seeding.laneNo).toBe(6) // Unchanged
  })

  it('should use mock payload for moving right', () => {
    const compEvent = createMockCompEvent()
    const result = updateTrackMoveHorz(compEvent, mockTrackMoveHorzPayloadMoveRight)

    // Check athletes moved 2 lanes to the right (factor: 2)
    expect(result.entries!['192707'].seeding.laneNo).toBe(6) // 4 + 2
    expect(result.entries!['113981'].seeding.laneNo).toBe(8) // 6 + 2
    expect(result.entries!['187224'].seeding.laneNo).toBe(5) // 3 + 2

    // Check checked-in lanes also moved
    expect(result.entries!['192707'].laneNoCheckedIn).toBe(6) // 4 + 2
    expect(result.entries!['113981'].laneNoCheckedIn).toBe(8) // 6 + 2
    expect(result.entries!['187224'].laneNoCheckedIn).toBe(5) // 3 + 2
  })

  it('should use mock payload for moving left', () => {
    const compEvent = createMockCompEvent()
    const result = updateTrackMoveHorz(compEvent, mockTrackMoveHorzPayloadMoveLeft)

    // Check athletes moved 1 lane to the left (factor: -1)
    expect(result.entries!['113981'].seeding.laneNo).toBe(5) // 6 - 1
    expect(result.entries!['192851'].seeding.laneNo).toBe(7) // 8 - 1
    expect(result.entries!['187527'].seeding.laneNo).toBe(4) // 5 - 1

    // Check checked-in lanes also moved
    expect(result.entries!['113981'].laneNoCheckedIn).toBe(5) // 6 - 1
    expect(result.entries!['192851'].laneNoCheckedIn).toBe(7) // 8 - 1
    expect(result.entries!['187527'].laneNoCheckedIn).toBe(4) // 5 - 1
  })

  it('should use mock payload for invalid moves', () => {
    const compEvent = createMockCompEvent()
    const result = updateTrackMoveHorz(compEvent, mockTrackMoveHorzPayloadInvalidMove)

    // Athletes should remain in original positions (invalid moves rejected)
    expect(result.entries!['194227'].seeding.laneNo).toBe(1) // Unchanged (would go to -4)
    expect(result.entries!['194251'].seeding.laneNo).toBe(2) // Unchanged (would go to -3)

    // Checked-in lanes should also remain unchanged
    expect(result.entries!['194227'].laneNoCheckedIn).toBe(0) // Unchanged
    expect(result.entries!['194251'].laneNoCheckedIn).toBe(2) // Unchanged
  })

  it('should handle mixed valid and invalid moves in same payload', () => {
    const compEvent = createMockCompEvent()

    const payload: SocketResultMessagePayloadTrackMoveHorz = {
      checkedIn: false,
      factor: -3,
      entryInfo: [
        { athleteId: 194227, entryId: 225042, teamId: 0 }, // Lane 1, would go to -2 (invalid)
        { athleteId: 113981, entryId: 222366, teamId: 0 }, // Lane 6, would go to 3 (valid)
        { athleteId: 192851, entryId: 223275, teamId: 0 } // Lane 8, would go to 5 (valid)
      ]
    }

    const result = updateTrackMoveHorz(compEvent, payload)

    // Invalid move should be rejected
    expect(result.entries!['194227'].seeding.laneNo).toBe(1) // Unchanged

    // Valid moves should be applied
    expect(result.entries!['113981'].seeding.laneNo).toBe(3) // 6 - 3
    expect(result.entries!['192851'].seeding.laneNo).toBe(5) // 8 - 3
  })

  it('should handle large positive factor movements', () => {
    const compEvent = createMockCompEvent()

    const payload: SocketResultMessagePayloadTrackMoveHorz = {
      checkedIn: false,
      factor: 10, // Large movement
      entryInfo: [
        { athleteId: 192707, entryId: 221930, teamId: 0 }, // Lane 4, goes to 14
        { athleteId: 194227, entryId: 225042, teamId: 0 } // Lane 1, goes to 11
      ]
    }

    const result = updateTrackMoveHorz(compEvent, payload)

    // Large movements should be allowed (no upper limit validation)
    expect(result.entries!['192707'].seeding.laneNo).toBe(14) // 4 + 10
    expect(result.entries!['194227'].seeding.laneNo).toBe(11) // 1 + 10
  })

  it('should show before and after positions for right movement', () => {
    const compEvent = createMockCompEvent()

    // Record original positions
    const originalPositions = {
      john: compEvent.entries!['192707'].seeding.laneNo, // John Doe
      jane: compEvent.entries!['113981'].seeding.laneNo, // Jane Smith
      bob: compEvent.entries!['187224'].seeding.laneNo, // Bob Johnson
      alice: compEvent.entries!['194227'].seeding.laneNo // Alice Brown (not moving)
    }

    const payload: SocketResultMessagePayloadTrackMoveHorz = {
      checkedIn: false,
      factor: 3, // Move 3 lanes to the right
      entryInfo: [
        { athleteId: 192707, entryId: 221930, teamId: 0 }, // John Doe
        { athleteId: 113981, entryId: 222366, teamId: 0 }, // Jane Smith
        { athleteId: 187224, entryId: 224433, teamId: 0 } // Bob Johnson
      ]
    }

    const result = updateTrackMoveHorz(compEvent, payload)

    // Verify before and after positions
    expect(originalPositions.john).toBe(4)
    expect(result.entries!['192707'].seeding.laneNo).toBe(7) // 4 + 3
    console.log(
      `John Doe: Lane ${originalPositions.john} → Lane ${result.entries!['192707'].seeding.laneNo}`
    )

    expect(originalPositions.jane).toBe(6)
    expect(result.entries!['113981'].seeding.laneNo).toBe(9) // 6 + 3
    console.log(
      `Jane Smith: Lane ${originalPositions.jane} → Lane ${result.entries!['113981'].seeding.laneNo}`
    )

    expect(originalPositions.bob).toBe(3)
    expect(result.entries!['187224'].seeding.laneNo).toBe(6) // 3 + 3
    console.log(
      `Bob Johnson: Lane ${originalPositions.bob} → Lane ${result.entries!['187224'].seeding.laneNo}`
    )

    // Alice Brown should remain unchanged
    expect(originalPositions.alice).toBe(1)
    expect(result.entries!['194227'].seeding.laneNo).toBe(1) // Unchanged
    console.log(
      `Alice Brown: Lane ${originalPositions.alice} → Lane ${result.entries!['194227'].seeding.laneNo} (unchanged)`
    )
  })

  it('should show before and after positions for left movement', () => {
    const compEvent = createMockCompEvent()

    // Record original positions
    const originalPositions = {
      jane: compEvent.entries!['113981'].seeding.laneNo, // Jane Smith
      charlie: compEvent.entries!['192851'].seeding.laneNo, // Charlie Wilson
      diana: compEvent.entries!['187527'].seeding.laneNo, // Diana Davis
      eve: compEvent.entries!['194251'].seeding.laneNo // Eve Miller (not moving)
    }

    const payload: SocketResultMessagePayloadTrackMoveHorz = {
      checkedIn: false,
      factor: -2, // Move 2 lanes to the left
      entryInfo: [
        { athleteId: 113981, entryId: 222366, teamId: 0 }, // Jane Smith
        { athleteId: 192851, entryId: 223275, teamId: 0 }, // Charlie Wilson
        { athleteId: 187527, entryId: 224710, teamId: 0 } // Diana Davis
      ]
    }

    const result = updateTrackMoveHorz(compEvent, payload)

    // Verify before and after positions
    expect(originalPositions.jane).toBe(6)
    expect(result.entries!['113981'].seeding.laneNo).toBe(4) // 6 - 2
    console.log(
      `Jane Smith: Lane ${originalPositions.jane} → Lane ${result.entries!['113981'].seeding.laneNo}`
    )

    expect(originalPositions.charlie).toBe(8)
    expect(result.entries!['192851'].seeding.laneNo).toBe(6) // 8 - 2
    console.log(
      `Charlie Wilson: Lane ${originalPositions.charlie} → Lane ${result.entries!['192851'].seeding.laneNo}`
    )

    expect(originalPositions.diana).toBe(5)
    expect(result.entries!['187527'].seeding.laneNo).toBe(3) // 5 - 2
    console.log(
      `Diana Davis: Lane ${originalPositions.diana} → Lane ${result.entries!['187527'].seeding.laneNo}`
    )

    // Eve Miller should remain unchanged
    expect(originalPositions.eve).toBe(2)
    expect(result.entries!['194251'].seeding.laneNo).toBe(2) // Unchanged
    console.log(
      `Eve Miller: Lane ${originalPositions.eve} → Lane ${result.entries!['194251'].seeding.laneNo} (unchanged)`
    )
  })

  it('should show before and after positions for invalid moves', () => {
    const compEvent = createMockCompEvent()

    // Record original positions
    const originalPositions = {
      alice: compEvent.entries!['194227'].seeding.laneNo, // Alice Brown
      eve: compEvent.entries!['194251'].seeding.laneNo, // Eve Miller
      john: compEvent.entries!['192707'].seeding.laneNo // John Doe (not in payload)
    }

    const payload: SocketResultMessagePayloadTrackMoveHorz = {
      checkedIn: false,
      factor: -4, // Move 4 lanes to the left (will cause invalid positions)
      entryInfo: [
        { athleteId: 194227, entryId: 225042, teamId: 0 }, // Alice Brown (lane 1 → -3, invalid)
        { athleteId: 194251, entryId: 225034, teamId: 0 } // Eve Miller (lane 2 → -2, invalid)
      ]
    }

    const result = updateTrackMoveHorz(compEvent, payload)

    // Verify positions remain unchanged due to invalid moves
    expect(originalPositions.alice).toBe(1)
    expect(result.entries!['194227'].seeding.laneNo).toBe(1) // Unchanged (would be -3)
    console.log(
      `Alice Brown: Lane ${originalPositions.alice} → Lane ${result.entries!['194227'].seeding.laneNo} (invalid move rejected)`
    )

    expect(originalPositions.eve).toBe(2)
    expect(result.entries!['194251'].seeding.laneNo).toBe(2) // Unchanged (would be -2)
    console.log(
      `Eve Miller: Lane ${originalPositions.eve} → Lane ${result.entries!['194251'].seeding.laneNo} (invalid move rejected)`
    )

    // John Doe should remain unchanged (not in payload)
    expect(originalPositions.john).toBe(4)
    expect(result.entries!['192707'].seeding.laneNo).toBe(4) // Unchanged
    console.log(
      `John Doe: Lane ${originalPositions.john} → Lane ${result.entries!['192707'].seeding.laneNo} (not in payload)`
    )
  })

  it('should show before and after positions including checked-in lanes', () => {
    const compEvent = createMockCompEvent()

    // Record original positions (both seeding and checked-in)
    const originalPositions = {
      john: {
        seeding: compEvent.entries!['192707'].seeding.laneNo,
        checkedIn: compEvent.entries!['192707'].laneNoCheckedIn
      },
      jane: {
        seeding: compEvent.entries!['113981'].seeding.laneNo,
        checkedIn: compEvent.entries!['113981'].laneNoCheckedIn
      },
      alice: {
        seeding: compEvent.entries!['194227'].seeding.laneNo,
        checkedIn: compEvent.entries!['194227'].laneNoCheckedIn // This is 0 (not checked in)
      }
    }

    const payload: SocketResultMessagePayloadTrackMoveHorz = {
      checkedIn: false,
      factor: 1, // Move 1 lane to the right
      entryInfo: [
        { athleteId: 192707, entryId: 221930, teamId: 0 }, // John Doe
        { athleteId: 113981, entryId: 222366, teamId: 0 }, // Jane Smith
        { athleteId: 194227, entryId: 225042, teamId: 0 } // Alice Brown
      ]
    }

    const result = updateTrackMoveHorz(compEvent, payload)

    // Verify seeding lane changes
    expect(originalPositions.john.seeding).toBe(4)
    expect(result.entries!['192707'].seeding.laneNo).toBe(5) // 4 + 1
    console.log(
      `John Doe seeding: Lane ${originalPositions.john.seeding} → Lane ${result.entries!['192707'].seeding.laneNo}`
    )

    expect(originalPositions.jane.seeding).toBe(6)
    expect(result.entries!['113981'].seeding.laneNo).toBe(7) // 6 + 1
    console.log(
      `Jane Smith seeding: Lane ${originalPositions.jane.seeding} → Lane ${result.entries!['113981'].seeding.laneNo}`
    )

    expect(originalPositions.alice.seeding).toBe(1)
    expect(result.entries!['194227'].seeding.laneNo).toBe(2) // 1 + 1
    console.log(
      `Alice Brown seeding: Lane ${originalPositions.alice.seeding} → Lane ${result.entries!['194227'].seeding.laneNo}`
    )

    // Verify checked-in lane changes (only for those who are checked in)
    expect(originalPositions.john.checkedIn).toBe(4)
    expect(result.entries!['192707'].laneNoCheckedIn).toBe(5) // 4 + 1
    console.log(
      `John Doe checked-in: Lane ${originalPositions.john.checkedIn} → Lane ${result.entries!['192707'].laneNoCheckedIn}`
    )

    expect(originalPositions.jane.checkedIn).toBe(6)
    expect(result.entries!['113981'].laneNoCheckedIn).toBe(7) // 6 + 1
    console.log(
      `Jane Smith checked-in: Lane ${originalPositions.jane.checkedIn} → Lane ${result.entries!['113981'].laneNoCheckedIn}`
    )

    // Alice Brown is not checked in (laneNoCheckedIn = 0), so it should remain 0
    expect(originalPositions.alice.checkedIn).toBe(0)
    expect(result.entries!['194227'].laneNoCheckedIn).toBe(0) // Unchanged
    console.log(
      `Alice Brown checked-in: Lane ${originalPositions.alice.checkedIn} → Lane ${result.entries!['194227'].laneNoCheckedIn} (not checked in)`
    )
  })
})
