<template>
  <CommonSimpleRow>
    <div class="e4s-flex-row">
      <div class="e4s-flex-column hybrid-start-race--first-col">
        <slot name="first-col"></slot>
      </div>

      <div class="e4s-flex-column e4s-gap--small e4s-flex-grow">
        <!--FirstRow-->
        <div class="e4s-flex-row">
          <slot name="athlete-name"></slot>
          <div class="e4s-flex-row--end">
            <slot name="performance"></slot>
          </div>
        </div>
        <!--/FirstRow-->

        <!--SecondRow-->
        <div class="e4s-flex-row e4s-gap--small e4s-justify-flex-row-vert-center">
          <div class="e4s-flex-row e4s-subheader--300 e4s-gap--standard">
            <slot name="name-sub">
              <slot name="affiliation"></slot>
              <div class="e4s-flex-row">
                <slot name="ageGroup"></slot>
              </div>
            </slot>
          </div>
          <div class="e4s-flex-row--end">
            <slot name="trials"></slot>

            <slot name="bib-checked-in"></slot>
          </div>
        </div>
        <!--/SecondRow-->

        <!--ThirdRow-->
        <div class="e4s-flex-row e4s-subheader--general e4s-subheader--300" v-if="showBibLaneTrack">
          <slot name="bib-heat-lane">
            <div
              class="e4s-flex-row e4s-gap--small hybrid-start-race--bottom-row e4s-justify-flex-row-vert-center"
            >
              <span>Bib:</span><slot name="bibNo"></slot>
            </div>
            <template v-if="isTrack">
              <div
                class="e4s-flex-row e4s-gap--small hybrid-start-race--bottom-row e4s-justify-flex-row-vert-center"
              >
                <span v-text="(isTrack ? 'Race' : 'Heat') + ':'"></span><slot name="heatNo"></slot>
              </div>

              <div
                class="e4s-flex-row e4s-gap--small hybrid-start-race--bottom-row e4s-justify-flex-row-vert-center"
              >
                <slot name="laneNoLabel"><span>Lane:</span></slot>
                <span class="e4s-lite-number">
                  <slot name="laneNo"></slot>
                </span>
              </div>
            </template>
          </slot>

          <div class="e4s-flex-row--end">
            <!--            :class="isTrack ? 'e4s-flex-column' : 'e4s-flex-row'"-->
            <div
              class="e4s-flex-column e4s-subheader--300 e4s-flex-row--end e4s-justify-flex-row-vert-center-x e4s-gap--small e4s-flex-wrap"
              v-if="hasPB || hasSB"
            >
              <div class="e4s-flex-row e4s-gap--small hybrid-start-race--po10-pb-sb" v-if="hasSB">
                <!--                <span>SB: </span>-->
                <!--                <div v-text="getPoSB"></div>-->
                <!--                <div v-text="getPoSBAchieved" class="pb-text"></div>-->
                <span v-text="'SB: ' + getPoSB + ' ' + getPoSBAchieved" class="pb-text"></span>
              </div>

              <div
                class="e4s-flex-row e4s-flex-row--end e4s-gap--small hybrid-start-race--po10-pb-sb"
                v-if="hasPB"
              >
                <!--                <span>PB: </span>-->
                <!--                <div v-text="getPoPB" class="pb-text"></div>-->
                <!--                <div v-text="getPoPBAchieved" class="pb-text"></div>-->
                <span v-text="'PB: ' + getPoPB + ' ' + getPoPBAchieved" class="pb-text"></span>
              </div>

              <!--      <PowerOfTenLink :urn="urn" :show-urn-link="false" image-height="16px" :show-pre-text="true">-->
              <!--        <template #pre-text>Link...</template>-->
              <!--      </PowerOfTenLink>-->
            </div>
          </div>
        </div>
        <!--/ThirdRow-->
      </div>
    </div>

    <slot name="extra-row"> </slot>
  </CommonSimpleRow>
</template>
<script setup lang="ts">
import CommonSimpleRow from '@/common/ui/CommonSimpleRow.vue'
import type { Pof10 } from '@/components/results/store-comp-results'
import { computed, type PropType } from 'vue'
import * as ResultsService from '@/components/results/results-service'
import PowerOfTenLink from '@/common/ui/PowerOfTenLink.vue'

const props = defineProps({
  showPosition: {
    type: Boolean,
    required: false
  },
  showBibLaneTrack: {
    type: Boolean,
    default: true
  },
  isTrack: {
    type: Boolean,
    required: false
  },
  pof10: {
    type: Object as PropType<Pof10 | null | undefined>,
    default: () => {
      const pof10: Pof10 = {
        pbPerf: '',
        pbAchieved: '',
        sbPerf: '',
        sbAchieved: ''
      }
      return pof10
    }
  },
  urn: {
    type: String,
    required: true
  }
})

const hasPB = computed(() => {
  return ResultsService.hasPo10Value(props.pof10, 'PB')
})

const hasSB = computed(() => {
  return ResultsService.hasPo10Value(props.pof10, 'SB')
})

const getPoPB = computed(() => {
  return ResultsService.getPo10Value(props.pof10, 'PB', props.isTrack)
})

const getPoSB = computed(() => {
  return ResultsService.getPo10Value(props.pof10, 'SB', props.isTrack)
})

const getPoPBAchieved = computed(() => {
  const achieved = ResultsService.formatPo10DateAchieved(props.pof10, 'PB', props.isTrack)
  return '(' + (achieved.length > 0 ? achieved : 'N/A') + ')'
})

const getPoSBAchieved = computed(() => {
  const achieved = ResultsService.formatPo10DateAchieved(props.pof10, 'SB', props.isTrack)
  return '(' + (achieved.length > 0 ? achieved : 'N/A') + ')'
})
</script>

<style>
.hybrid-start-race--first-col {
  width: 60px;
}

.hybrid-start-race--position {
  width: 20px;
}

.hybrid-start-race--lane-no {
  width: 20px;
}

.hybrid-start-race--bib-no {
  width: 40px;
}

.hybrid-start-race--po10-pb-sb-x {
  width: 50px;
}

.hybrid-start-race--bottom-row {
  width: 60px;
}

.pb-text {
  text-align: end;
}
</style>
