import { describe, it, expect } from 'vitest'
import {
  convertArrayToObject,
  convertArrayToObjectArray,
  simpleClone
} from '@/services/common-service-utils'
import {
  convertEntries,
  convertEntry,
  convertExternalJsonToCompetitionOnTheDay,
  convertResult,
  convertSchedule,
  convertTrials
} from './competition-converter.service'
import type {
  ApiCompetitionEntry,
  ApiCompetitionOnTheDay,
  ApiCompetitionResult,
  ApiCompetitionSchedule,
  ApiCompetitionTrial,
  ApiServerResponse
} from '@/components/results/middleware/competition-converter.models'
import { mockExternalData } from '@/components/results/middleware/mocks/mockExternalData'
import { mockTrialsData } from '@/components/results/middleware/trials/mockTrialsData'

// Mock external JSON data (similar to 617.json)
const mockExternalJson: ApiServerResponse = mockExternalData

describe('Competition Converter Service', () => {
  it('should convert external JSON to CompetitionOnTheDay format', () => {
    const apiServerResponse = simpleClone(mockExternalJson)

    const apiCompetitionOnTheDay = apiServerResponse.data
    expect(apiCompetitionOnTheDay.competition.compId).toBe(617)
    expect(apiCompetitionOnTheDay.competition.compName).toBe('English Schools 2024')

    const competition = convertExternalJsonToCompetitionOnTheDay(apiCompetitionOnTheDay)

    // Test basic competition properties
    expect(competition.id).toBe(617)
    expect(competition.name).toBe('English Schools 2024')
    expect(competition.date).toBe('2024-07-11T23:00:00.000Z')
    expect(competition.location).toBe('ALEXANDER STADIUM')
    expect(competition.organiser).toBe('English Schools Athletic Association')
    // expect(competition.logo).toBe('/wp-json/view/312709/1748342936')
    // expect(competition.checkIn).toBe(true)
    // expect(competition.domain).toBe('athletics.uk')

    // Test events conversion
    // expect(Object.keys(competition.events).length).toBe(2)
    // expect(competition.events['1001']).toBeDefined()
    // expect(competition.events['1002']).toBeDefined()

    // Test first event
    // const event1 = competition.events['1001']
    // expect(event1.egId).toBe(1001)
    // expect(event1.name).toBe('100m Men')
    // expect(event1.eventNo).toBe(1)
    // expect(event1.typeNo).toBe('T1')
    // expect(event1.eventDate).toBe('2023-07-15')

    // Test entries
    // expect(Object.keys(event1.entries || {}).length).toBe(2)

    // Test results
    // expect(Object.keys(event1.results || {}).length).toBe(1)
    // expect(event1.results?.['1']?.length).toBe(2)

    /*
    // Test teams
    expect(event1.teams?.length).toBe(2)
    expect(event1.teams?.[0].id).toBe(201)
    expect(event1.teams?.[0].name).toBe('Team North')
    expect(event1.teams?.[0].members).toContain(101)

    // Test field event
    const event2 = competition.events['1002']
    expect(event2.typeNo).toBe('F7')
    const fieldResult = event2.results?.['1']?.[0]
    expect(fieldResult?.fieldResultAthlete?.attempts).toEqual([
      '6.75',
      '6.82',
      '6.70',
      'X',
      '6.65',
      '6.80'
    ])
    */
  })

  it('should convert entries correctly', () => {
    const apiCompetitionEntry: ApiCompetitionEntry = {
      entryId: 221191,
      compId: 617,
      ceId: 70791,
      egId: '13721',
      athleteId: 166724,
      firstName: 'William',
      surName: 'Allinson',
      ageGroupName: 'ES Inters',
      athleteBibNo: 56,
      teamBibNo: '77',
      athleteClub: 'Surrey',
      checkedIn: 1,
      collected: 1,
      present: 1,
      pb: '13.55',
      heatNo: 1,
      laneNo: 2,
      heatNoCheckedIn: 0,
      laneNoCheckedIn: 0,
      classification: 0,
      aoCode: 'EA'
    }
    const entry = convertEntry(apiCompetitionEntry)
    expect(entry.entryId).toBe(221191)
    expect(entry.athleteId).toBe(166724)
    // expect(entry.firstName).toBe('William')
    // expect(entry.surName).toBe('Allinson')
    expect(entry.teamBibNo).toBe('77')
  })

  it('should convert entries correctly', () => {
    const apiServerResponse = simpleClone(mockExternalJson)

    const apiCompetitionOnTheDay = apiServerResponse.data
    const entriesByEgId = convertArrayToObjectArray('egId', apiCompetitionOnTheDay.entries)
    const athletesById = convertArrayToObject('id', apiCompetitionOnTheDay.athletes)

    // expect(entriesByEgId['13721']).toBe(1453)

    const entries = convertEntries(entriesByEgId['13721'], athletesById)
    expect(entries[166724].firstName).toBe('William')
  })

  // const entriesByEgId = convertArrayToObjectArray('egId', apiCompetitionOnTheDay.entries)

  it('should convert schedules correctly', () => {
    const apiCompetitionSchedule: ApiCompetitionSchedule = {
      egId: 13730,
      compId: 617,
      eventNo: 52,
      eventName: '100m JB Final',
      eventStartDate: '2024-07-13T10:34:00.000Z',
      typeNo: 'T52',
      egOptions: {
        maxathletes: 0,
        maxInHeat: 0,
        seed: {
          laneCount: 0,
          seeded: false,
          qualifyToEg: {
            id: 0,
            compId: 0,
            name: '',
            eventNo: 0,
            isMultiEvent: false,
            rules: {
              auto: 0,
              nonAuto: 0
            }
          }
        },
        checkIn: {
          from: -1,
          to: -1,
          seedOnEntries: false
        }
      },
      ceId: 70686,
      ageGroupId: 213,
      ageGroup: 'ES Juniors',
      ageGroupKey: 'ES Juniors'
    }

    const event = convertSchedule(apiCompetitionSchedule)
    expect(event.egId).toBe(13730)
    expect(event.name).toBe('100m JB Final')
    expect(event.eventNo).toBe(52)
    expect(event.typeNo).toBe('T52')
    expect(event.eventDate).toBe('2024-07-13T10:34:00.000Z')
  })

  it('should convert result correctly', () => {
    const apiCompetitionResult: ApiCompetitionResult = {
      rhId: 16427,
      compId: 617,
      egId: 13737,
      rdId: 196151,
      heatNo: 1,
      laneNo: 5,
      position: 1,
      athleteId: 107472,
      athlete: 'Noah HANSON',
      club: 'Essex',
      bibNo: '27',
      score: '13.64',
      qualify: 'Q',
      scoreText: '',
      ageGroup: '',
      gender: '',
      wind: '+1.0',
      eaAward: 9,
      crOptions:
        '{"scoreText":"","place":1,"bibNo":27,"lane":5,"club":"Essex","athlete":"Noah HANSON","firstName":"Noah","surName":"HANSON","athleteId":107472,"time":13.64,"ageGroup":"Inters","ws":1,"eventTime":"10:00:00"}'
    }

    const result = convertResult(apiCompetitionResult)
    expect(result.laneNo).toBe(5)
    expect(result.position).toBe(1)
    expect(result.wind).toBe('+1.0')
    expect(result.athlete).toBe('Noah HANSON')
    expect(result.athleteId).toBe(107472)
    expect(result.affilation).toBe('Essex')
    expect(result.bibNo).toBe('27')
    expect(result.score).toBe('13.64')
    expect(result.qualify).toBe('Q')
  })

  // test convertTrials
  it('should convert trials correctly', () => {
    const apiCompetitionTrials: ApiCompetitionTrial[] = [
      {
        compId: 617,
        egId: 13737,
        athleteId: 107472,
        resultKey: 't1',
        resultValue: '13.64',
        options: null
      },
      {
        compId: 617,
        egId: 13737,
        athleteId: 107472,
        resultKey: 't2',
        resultValue: '13.65',
        options: null
      },
      {
        compId: 617,
        egId: 13737,
        athleteId: 107472,
        resultKey: 't3',
        resultValue: '13.66',
        options: null
      }
    ]

    const trials = convertTrials(apiCompetitionTrials)
    // expect(trials).toBe(111)

    const eventGroup13737 = trials[13737]
    expect(eventGroup13737).toBeDefined()

    // expect athlete 107472 to have 3 trials
    const athlete107472 = eventGroup13737[107472]
    expect(athlete107472).toBeDefined()
    expect(Object.keys(athlete107472).length).toBe(3)
    expect(athlete107472['t1']).toBe('13.64')
    expect(athlete107472['t2']).toBe('13.65')
    expect(athlete107472['t3']).toBe('13.66')
  })

  // now use mockTrialsData
  it('should convert trials correctly', () => {
    const trials = convertTrials(simpleClone(mockTrialsData))

    const eventGroup16070 = trials[16070]
    expect(eventGroup16070).toBeDefined()

    // expect athlete 198072 to have 6 trials
    const athlete198072 = eventGroup16070[198072]
    expect(athlete198072).toBeDefined()

    // expect(trials).toBe('2')
  })
})
