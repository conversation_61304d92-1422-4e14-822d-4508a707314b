import { type AO_CODE, type IsoDateTime, type IsoDateTimeUtc } from '@/common/common-models'
import type {
  EntryGender,
  FieldTypeNo,
  LiveFeedMessageServer,
  TrackTypeNo
} from '@/components/results/store-comp-results'

export interface ApiServerResponse {
  data: ApiCompetitionOnTheDay
}

export interface ApiCompetitionOnTheDay {
  competition: ApiCompetitionDetail
  schedules: ApiCompetitionSchedule[]
  results: ApiCompetitionResult[]
  entries: ApiCompetitionEntry[]
  teams: ApiCompetitionTeam[]
  athletes: ApiCompetitionAthlete[]
  trials: ApiCompetitionTrial[]
  socketMessages: LiveFeedMessageServer[]
  cachedDataSummary: ApiCompetitionCachedDataSummary
  competitionClientConfig: CompetitionClientConfig
}

export interface ApiCompetitionCachedDataSummary {
  cachedAt: IsoDateTimeUtc
  competitionId: number
  messageCount: number
}

export interface CompetitionClientConfig {
  refreshIntervalMs: number
}

export interface ApiCompetitionDetail {
  compId: number
  compName: string
  compdate: IsoDateTime
  locId: number
  locName: string
  compclubid: number
  organiser: string
  logo: string
  // Add other fields as needed
  options: ApiCompetitionOptions
}

export interface ApiCompetitionOptions {
  checkIn: {
    enabled: boolean
  }
}

export interface ApiCompetitionSchedule {
  egId: number
  compId: number
  eventNo: number
  eventName: string
  eventStartDate: IsoDateTime
  typeNo: TrackTypeNo | FieldTypeNo
  egOptions: ApiCompetitionScheduleOptions | string
  ceId: number
  ageGroupId: number
  ageGroup: string
  ageGroupKey: string
}

/**
 * {
 *   "maxathletes": 21,
 *   "maxInHeat": 8,
 *   "seed": {
 *     "laneCount": 8,
 *     "qualifyToEg": {
 *       "rules": {
 *         "auto": 0,
 *         "nonAuto": 0
 *       },
 *       "eventNo": 0,
 *       "isMultiEvent": false
 *     }
 *   },
 *   "checkIn": {
 *     "from": -1,
 *     "to": -1,
 *     "seedOnEntries": false
 *   }
 * }
 */
export interface ApiCompetitionScheduleOptions {
  maxathletes?: number
  maxInHeat?: number
  seed: {
    laneCount?: number
    type?: 'H' | string
    seeded?: boolean
    doubleup?: string | string[] | number[]
    qualifyToEg: {
      id?: number
      compId?: number
      name?: string
      eventNo: number
      rules?: QualifyRules
      isMultiEvent?: boolean
    }
  }
  checkIn: {
    from: number
    to: number
    seedOnEntries: boolean
    checkInMins?: number
  }
  progressions?: unknown
  trials?: TrialsOptions
  trialInfo?: string // E.g.  'External Throws Area.'
  venue?: string
  resultsOpen?: boolean
}

export interface QualifyRules {
  auto: number
  nonAuto: number
}

export interface TrialsOptions {
  max?: number | null
  min?: number | null
  athleteCnt?: number
  jumpOrder?: string
}

export interface ApiCompetitionEntry {
  entryId: number
  compId: number
  ceId: number
  egId: number | string
  // eventName: string
  athleteId: null | number | string
  firstName: null | string
  surName: null | string
  ageGroupName: string // "ES Inters"
  athleteBibNo: number | string
  teamBibNo: null | string
  athleteClub: string // "Surrey",
  checkedIn: 1 | 0
  collected: 1 | 0
  // teamName: string
  present: null | 1
  pb: string // "13.55",
  heatNo: null | number
  laneNo: null | number
  heatNoCheckedIn: number
  laneNoCheckedIn: number
  classification: number
  aoCode: AO_CODE
}

export interface ApiCompetitionResult {
  rhId: number
  compId: number
  egId: number
  rdId: number
  heatNo: number
  laneNo: number
  position: number
  athleteId: number
  athlete: string
  club: string
  bibNo: string
  score: string
  qualify: string
  scoreText: string
  ageGroup: string
  gender: string
  wind: string
  eaAward: number
  // resultKey: null | string
  // resultValue: null | string
  crOptions: null | string
}

export interface ApiCompetitionTeam {
  entryId: number
  compId: number
  ceId: number
  egId: number
  eventName: string
  athleteId: null | number
  firstName: null | string
  surName: null | string
  teamBibNo: null | string
  teamName: string
  present: null | number
  heatno: null | number
  laneno: null | number
  heatnocheckedin: null | number
  lanenocheckedin: null | number
}

export interface ApiTeamMember {
  id: number
  teamId: number
  athleteId: number
  athleteName: string
  role: string
  // Add other fields based on your SQL query results
}

/*
      {
        id: 185662,
        compId: 617,
        firstName: 'Tom',
        surName: 'Croft',
        dob: '2007-11-01T00:00:00.000Z',
        urn: '3828384'
      }
 */
export interface ApiCompetitionAthlete {
  id: number
  compId: number
  firstName: string
  surName: string
  dob: IsoDateTimeUtc
  urn: string | number
  gender: EntryGender
  aoCode: AO_CODE
  bibNo: number | string
}

export type TrialOptions = unknown

export interface ApiCompetitionTrial {
  compId: number
  egId: number
  athleteId: number
  resultKey: string
  resultValue: string
  options: null | TrialOptions
}
