<template>
  <div class="e4s-flex-row e4s-justify-flex-center e4s-flex-row--vert-center" style="min-height: 100vh; background-color: var(--e4s-body--backgroundColor);">
    <CardGenericV2>
      <template #header>
        <div class="e4s-flex-column e4s-gap--standard" style="text-align: center;">
          <h1 class="e4s-header--300">Auth Success Test</h1>
        </div>
      </template>

      <template #content>
        <div class="e4s-flex-column e4s-gap--large" style="min-width: 400px;">
          <div class="e4s-body--100">
            This page allows you to test the auth-success functionality by simulating OAuth callback URLs.
          </div>

          <div class="e4s-flex-column e4s-gap--standard">
            <h3 class="e4s-header--400">Test Scenarios</h3>
            
            <div class="e4s-flex-column e4s-gap--small">
              <ButtonGenericV2
                button-type="primary"
                text="Test with Valid <PERSON> + Redirect to Admin"
                class="e4s-button--auto"
                @click="testValidTokenWithRedirect"
              />
              
              <ButtonGenericV2
                button-type="primary"
                text="Test with Valid Token + No Redirect"
                class="e4s-button--auto"
                @click="testValidTokenNoRedirect"
              />
              
              <ButtonGenericV2
                button-type="secondary"
                text="Test with Missing Token"
                class="e4s-button--auto"
                @click="testMissingToken"
              />
              
              <ButtonGenericV2
                button-type="secondary"
                text="Test with Invalid Token"
                class="e4s-button--auto"
                @click="testInvalidToken"
              />
            </div>
          </div>

          <div class="e4s-flex-column e4s-gap--standard">
            <h3 class="e4s-header--400">Manual Test</h3>
            
            <div class="e4s-flex-column e4s-gap--small">
              <label class="e4s-body--100">Token:</label>
              <input 
                v-model="customToken" 
                type="text" 
                placeholder="Enter JWT token"
                class="e4s-input-field e4s-input-field--primary"
                style="padding: 8px; border: 1px solid var(--slate-300); border-radius: 4px;"
              />
              
              <label class="e4s-body--100">Redirect URL:</label>
              <input 
                v-model="customRedirect" 
                type="text" 
                placeholder="Enter redirect URL (optional)"
                class="e4s-input-field e4s-input-field--primary"
                style="padding: 8px; border: 1px solid var(--slate-300); border-radius: 4px;"
              />
              
              <ButtonGenericV2
                button-type="primary"
                text="Test Custom Parameters"
                class="e4s-button--auto"
                @click="testCustomParameters"
                :disabled="!customToken"
              />
            </div>
          </div>

          <div class="e4s-info-section" style="background: var(--slate-50); border: 1px solid var(--slate-200);">
            <div class="e4s-body--200">
              <strong>Note:</strong> These tests will navigate to the auth-success page with different parameters.
              In a real scenario, the OAuth provider would redirect to this page with a valid token.
            </div>
          </div>
        </div>
      </template>
    </CardGenericV2>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import CardGenericV2 from '@/common/ui/card-generic-v2.vue'
import ButtonGenericV2 from '@/common/ui/buttons/button-generic-v2.vue'

const router = useRouter()

// Custom test parameters
const customToken = ref('')
const customRedirect = ref('')

// Sample JWT token for testing (this would normally come from OAuth provider)
const sampleValidToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
const sampleInvalidToken = 'invalid.token.here'

const testValidTokenWithRedirect = (): void => {
  const params = new URLSearchParams({
    token: sampleValidToken,
    redirect: '/admin'
  })
  router.push(`/auth-success?${params.toString()}`)
}

const testValidTokenNoRedirect = (): void => {
  const params = new URLSearchParams({
    token: sampleValidToken
  })
  router.push(`/auth-success?${params.toString()}`)
}

const testMissingToken = (): void => {
  const params = new URLSearchParams({
    redirect: '/admin'
  })
  router.push(`/auth-success?${params.toString()}`)
}

const testInvalidToken = (): void => {
  const params = new URLSearchParams({
    token: sampleInvalidToken,
    redirect: '/admin'
  })
  router.push(`/auth-success?${params.toString()}`)
}

const testCustomParameters = (): void => {
  const params = new URLSearchParams({
    token: customToken.value
  })
  
  if (customRedirect.value) {
    params.append('redirect', customRedirect.value)
  }
  
  router.push(`/auth-success?${params.toString()}`)
}
</script>

<style scoped>
.e4s-flex-row--vert-center {
  align-items: center;
}

.e4s-info-section {
  padding: var(--e4s-gap--standard);
  border-radius: var(--e4s-card--primary__border-radius);
}
</style>
