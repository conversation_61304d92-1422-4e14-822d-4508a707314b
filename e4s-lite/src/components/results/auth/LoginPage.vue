<template>
  <div class="e4s-flex-row e4s-justify-flex-center e4s-flex-row--vert-center" style="min-height: 100vh; background-color: var(--e4s-body--backgroundColor);">
    <CardGenericV2>
      <template #header>
        <div class="e4s-flex-column e4s-gap--standard" style="text-align: center;">
          <h1 class="e4s-header--300">Login</h1>
        </div>
      </template>

      <template #content>
        <div class="e4s-flex-column e4s-gap--large" style="min-width: 300px;">
          <div v-if="user" class="e4s-flex-column e4s-gap--standard">
            <h2 class="e4s-header--400">Welcome, {{ user.name }}</h2>
            <p class="e4s-body--100">Email: {{ user.email }}</p>
            <ButtonGenericV2
              button-type="destructive"
              text="Logout"
              class="e4s-button--auto"
              @click="logout"
            />
          </div>

          <div v-else class="e4s-flex-column e4s-gap--standard">
            <ButtonGenericV2
              button-type="primary"
              :text="isLoading ? 'Loading...' : '🔐 Login with Google'"
              class="e4s-button--auto"
              :disabled="isLoading"
              @click="loginWithGoogle"
            />
            <!-- Add more login options here in the future -->
          </div>

          <div v-if="error" class="e4s-info-section e4s-info-section--error">
            <div class="e4s-body--100">{{ error }}</div>
          </div>
        </div>
      </template>
    </CardGenericV2>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import CardGenericV2 from '@/common/ui/card-generic-v2.vue'
import ButtonGenericV2 from '@/common/ui/buttons/button-generic-v2.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Computed properties
const user = computed(() => authStore.user)
const error = computed(() => authStore.error)
const isLoading = computed(() => authStore.isLoading)

// Initialize auth store and handle redirects
onMounted(async () => {
  await authStore.initialize()

  // If user is already authenticated, redirect to return URL or home
  if (authStore.isAuthenticated) {
    const returnUrl = route.query.returnUrl as string
    if (returnUrl) {
      router.push(returnUrl)
    } else {
      router.push({ name: 'home' })
    }
  }
})

// Login with Google
const loginWithGoogle = (): void => {
  const returnUrl = route.query.returnUrl as string
  authStore.login(returnUrl)
}

// Logout
const logout = (): void => {
  authStore.logout()
}
</script>

<style scoped>
/* All styling is now handled by the project's e4s- utility classes and CSS variables */
/* This ensures consistency with the project's design system */

.e4s-flex-row--vert-center {
  align-items: center;
}

.e4s-info-section {
  padding: var(--e4s-gap--standard);
  border-radius: var(--e4s-card--primary__border-radius);
  border: 1px solid;
}

.e4s-info-section--error {
  background: var(--e4s-info-section--error__background);
  color: var(--e4s-info-section--error__text-color);
  border-color: var(--e4s-info-section--error__border-color);
}
</style>
