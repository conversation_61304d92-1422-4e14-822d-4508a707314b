<template>
  <div class="e4s-flex-row e4s-justify-flex-center e4s-flex-row--vert-center" style="min-height: 100vh; background-color: var(--e4s-body--backgroundColor);">
    <CardGenericV2>
      <template #header>
        <div class="e4s-flex-column e4s-gap--standard" style="text-align: center;">
          <h1 class="e4s-header--300">Authentication Success</h1>
        </div>
      </template>

      <template #content>
        <div class="e4s-flex-column e4s-gap--large" style="min-width: 300px;">
          <!-- Loading State -->
          <div v-if="isProcessing" class="e4s-flex-column e4s-gap--standard" style="text-align: center;">
            <div class="e4s-body--100">Processing authentication...</div>
            <div class="e4s-body--200">Please wait while we log you in.</div>
          </div>

          <!-- Success State -->
          <div v-else-if="isSuccess && !error" class="e4s-flex-column e4s-gap--standard" style="text-align: center;">
            <div class="e4s-info-section e4s-info-section--success">
              <div class="e4s-body--100">✅ Successfully authenticated!</div>
            </div>
            <div class="e4s-body--200">
              Redirecting you to {{ redirectDestination }}...
            </div>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="e4s-flex-column e4s-gap--standard">
            <div class="e4s-info-section e4s-info-section--error">
              <div class="e4s-body--100">{{ error }}</div>
            </div>
            <div class="e4s-flex-column e4s-gap--standard">
              <ButtonGenericV2
                button-type="primary"
                text="Try Again"
                class="e4s-button--auto"
                @click="retryAuthentication"
              />
              <ButtonGenericV2
                button-type="secondary"
                text="Go Home"
                class="e4s-button--auto"
                @click="goHome"
              />
            </div>
          </div>

          <!-- Debug Info (only in development) -->
          <div v-if="isDevelopment" class="e4s-flex-column e4s-gap--small" style="margin-top: 2rem; padding: 1rem; background: var(--slate-100); border-radius: 4px;">
            <div class="e4s-body--200" style="font-weight: bold;">Debug Info:</div>
            <div class="e4s-body--300">Token: {{ tokenParam ? '***' + tokenParam.slice(-8) : 'Not found' }}</div>
            <div class="e4s-body--300">Redirect: {{ redirectParam || 'Not specified' }}</div>
            <div class="e4s-body--300">Current URL: {{ currentUrl }}</div>
          </div>
        </div>
      </template>
    </CardGenericV2>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import CardGenericV2 from '@/common/ui/card-generic-v2.vue'
import ButtonGenericV2 from '@/common/ui/buttons/button-generic-v2.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// State
const isProcessing = ref(true)
const isSuccess = ref(false)
const error = ref<string | null>(null)

// Extract URL parameters
const tokenParam = computed(() => route.query.token as string)
const redirectParam = computed(() => route.query.redirect as string)
const currentUrl = computed(() => window.location.href)

// Environment check
const isDevelopment = computed(() => import.meta.env.DEV)

// Computed redirect destination for display
const redirectDestination = computed(() => {
  if (redirectParam.value) {
    try {
      // If it's a full URL, extract the path
      const url = new URL(redirectParam.value)
      return url.pathname + url.search + url.hash
    } catch {
      // If it's already a path, use as is
      return redirectParam.value
    }
  }
  return 'home page'
})

// Process authentication on mount
onMounted(async () => {
  await processAuthentication()
})

const processAuthentication = async (): Promise<void> => {
  try {
    isProcessing.value = true
    error.value = null

    // Validate required parameters
    if (!tokenParam.value) {
      throw new Error('Authentication token is missing from URL parameters.')
    }

    console.log('Processing authentication with token:', tokenParam.value.slice(-8))
    console.log('Redirect parameter:', redirectParam.value)

    // Store the token
    authStore.setToken(tokenParam.value)

    // Verify the token with the server
    const isValid = await authStore.verifyToken()

    if (!isValid) {
      throw new Error('Invalid authentication token. Please try logging in again.')
    }

    // Authentication successful
    isSuccess.value = true
    isProcessing.value = false

    // Clean the URL by removing the token parameter for security
    cleanUrlParameters()

    // Redirect after a short delay to show success message
    setTimeout(() => {
      performRedirect()
    }, 2000)

  } catch (err) {
    console.error('Authentication processing failed:', err)
    error.value = err instanceof Error ? err.message : 'An unexpected error occurred during authentication.'
    isProcessing.value = false
    isSuccess.value = false

    // Clean the URL even on error to remove sensitive token
    cleanUrlParameters()
  }
}

const cleanUrlParameters = (): void => {
  // Remove token and other sensitive parameters from URL for security
  const newUrl = new URL(window.location.href)
  newUrl.searchParams.delete('token')
  
  // Keep redirect parameter for potential retry
  if (!error.value) {
    newUrl.searchParams.delete('redirect')
  }
  
  window.history.replaceState({}, document.title, newUrl.toString())
}

const performRedirect = (): void => {
  if (redirectParam.value) {
    try {
      // Check if it's a full URL or a path
      if (redirectParam.value.startsWith('http')) {
        // External URL - redirect directly
        window.location.href = redirectParam.value
      } else {
        // Internal path - use router
        router.push(redirectParam.value)
      }
    } catch (err) {
      console.error('Redirect failed:', err)
      // Fallback to home page
      router.push({ name: 'home' })
    }
  } else {
    // No redirect specified, go to home
    router.push({ name: 'home' })
  }
}

const retryAuthentication = (): void => {
  // Clear error state and retry
  error.value = null
  isSuccess.value = false
  
  // If we still have the token in the URL, try again
  if (tokenParam.value) {
    processAuthentication()
  } else {
    // No token available, redirect to login
    router.push({ 
      name: 'login',
      query: redirectParam.value ? { returnUrl: redirectParam.value } : {}
    })
  }
}

const goHome = (): void => {
  router.push({ name: 'home' })
}
</script>

<style scoped>
.e4s-flex-row--vert-center {
  align-items: center;
}

.e4s-info-section {
  padding: var(--e4s-gap--standard);
  border-radius: var(--e4s-card--primary__border-radius);
  border: 1px solid;
}

.e4s-info-section--error {
  background: var(--e4s-info-section--error__background);
  color: var(--e4s-info-section--error__text-color);
  border-color: var(--e4s-info-section--error__border-color);
}

.e4s-info-section--success {
  background: var(--green-50);
  color: var(--green-800);
  border-color: var(--green-200);
}
</style>
