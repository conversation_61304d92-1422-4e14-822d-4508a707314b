<template>
  <div class="e4s-flex-row e4s-justify-flex-center e4s-flex-row--vert-center" style="min-height: 100vh; background-color: var(--e4s-body--backgroundColor);">
    <CardGenericV2>
      <template #header>
        <div class="e4s-flex-column e4s-gap--standard" style="text-align: center;">
          <h1 class="e4s-header--300">Access Denied</h1>
        </div>
      </template>

      <template #content>
        <div class="e4s-flex-column e4s-gap--large" style="min-width: 300px;">
          <div class="e4s-info-section e4s-info-section--error">
            <div class="e4s-body--100">
              {{ message || 'You do not have permission to access this page.' }}
            </div>
          </div>

          <div class="e4s-flex-column e4s-gap--standard">
            <ButtonGenericV2
              button-type="primary"
              text="Go Home"
              class="e4s-button--auto"
              @click="goHome"
            />
            
            <ButtonGenericV2
              v-if="!authStore.isAuthenticated"
              button-type="secondary"
              text="Login"
              class="e4s-button--auto"
              @click="goToLogin"
            />
          </div>
        </div>
      </template>
    </CardGenericV2>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import CardGenericV2 from '@/common/ui/card-generic-v2.vue'
import ButtonGenericV2 from '@/common/ui/buttons/button-generic-v2.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const message = computed(() => route.query.message as string)

const goHome = (): void => {
  router.push({ name: 'home' })
}

const goToLogin = (): void => {
  router.push({ name: 'login' })
}
</script>

<style scoped>
.e4s-flex-row--vert-center {
  align-items: center;
}

.e4s-info-section {
  padding: var(--e4s-gap--standard);
  border-radius: var(--e4s-card--primary__border-radius);
  border: 1px solid;
}

.e4s-info-section--error {
  background: var(--e4s-info-section--error__background);
  color: var(--e4s-info-section--error__text-color);
  border-color: var(--e4s-info-section--error__border-color);
}
</style>
