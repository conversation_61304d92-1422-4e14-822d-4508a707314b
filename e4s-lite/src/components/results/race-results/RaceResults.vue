<template>
  <div v-for="(individualResult, index) in resultsInternal" :key="individualResult.position">
    <RaceResult
      :id="'athlete-result-' + individualResult.athleteId"
      :individual-result="individualResult"
      :api-competition-athlete="storeCompResultsState.athletes[individualResult.athleteId]"
      :heat-no="'heatNo' in individualResult ? individualResult.heatNo! : heatNo"
      :is-track="isTrack"
      :is-team-event="isTeamEvent"
      :individual-entry="getIndividualEntryForAthlete(individualResult.athleteId)"
      :athlete-trial-map="eventTrialMap[individualResult.athleteId]"
    />
    <!--    ============= ch: {{ individualResult.hasScoreChanged }} ani:-->
    <!--    {{ individualResult.animateNewScore }}-->
  </div>
</template>

<script setup lang="ts">
import type {
  AthleteTrialMap,
  IndividualEntry,
  IndividualResult,
  StoreCompResultsState
} from '@/components/results/store-comp-results'
import { type PropType, ref, watch } from 'vue'

import {
  athleteReorderDetails,
  type IndividualResultWithHeat,
  sortRaceResultsByPosition
} from '@/components/results/results-service'
import type { AthleteIdString } from '@/services/common'
import RaceResult from '@/components/results/race-results/RaceResult.vue'
import { simpleClone } from '@/services/common-service-utils'

export interface IndividualResultAnimated extends IndividualResult, IndividualResultWithHeat {
  hasScoreChanged: boolean
  hasChangedPosition: boolean
  hasPositionImproved: boolean
  animateNewScore: boolean
}

const props = defineProps({
  storeCompResultsState: {
    type: Object as PropType<StoreCompResultsState>,
    required: true
  },
  individualResults: {
    type: Array as PropType<IndividualResult[] | IndividualResultWithHeat[]>,
    required: true
  },
  entries: {
    type: Object as PropType<Record<AthleteIdString, IndividualEntry>>,
    required: false
  },
  isTrack: {
    type: Boolean,
    required: false
  },
  isTeamEvent: {
    type: Boolean,
    required: true
  },
  heatNo: {
    type: [Number, String],
    required: true
  },
  eventTrialMap: {
    type: Object as PropType<Record<AthleteIdString, AthleteTrialMap>>,
    default: () => {
      return {}
    }
  }
})

// const lastResults = ref<IndividualResultAnimated[]>([])
const resultsInternal = ref<IndividualResultAnimated[]>([])

// map the incoming individualResults to IndividualResultAnimated and set the internal results
const individualResultsAnimated = mapIndividualResultsAnimated(simpleClone(props.individualResults))
setInternalResults(individualResultsAnimated)

function mapIndividualResultsAnimated(
  individualResults: (IndividualResult | IndividualResultWithHeat)[]
): IndividualResultAnimated[] {
  return individualResults.map((result) => {
    return {
      ...result,
      hasScoreChanged: false,
      hasChangedPosition: false,
      hasPositionImproved: false,
      animateNewScore: false
    } as IndividualResultAnimated
  })
}

watch(
  () => props.individualResults,
  (newValue, oldValue) => {
    // console.error('individualResults changed', newValue, oldValue)
    animateChange(newValue, oldValue)
  }
)

function animateChange(
  newValue: (IndividualResult | IndividualResultWithHeat)[],
  oldValue: (IndividualResult | IndividualResultWithHeat)[]
): void {
  const animateThisAthlete = newValue.find((result, index) => {
    return result.animateThisAthlete
  })
  if (!animateThisAthlete) {
    setInternalResults(mapIndividualResultsAnimated(simpleClone(newValue)))
    return
  }

  const athleteId = animateThisAthlete.athleteId
  const reorderDetails = athleteReorderDetails(oldValue, newValue, athleteId)

  // map oldValue and find the athleteId of the first athlete with animateThisAthlete set to true
  const currentValues = simpleClone(resultsInternal.value).map((result) => {
    if (result.athleteId === athleteId) {
      return {
        ...result,
        animateThisAthlete: false,
        hasScoreChanged: reorderDetails.hasScoreChanged,
        hasPositionImproved: reorderDetails.newPosition < reorderDetails.originalPosition,
        animateNewScore: false
      }
    }
    return result
  })

  // Sort the results by position
  currentValues.sort((a, b) => {
    return a.position - b.position
  })

  resultsInternal.value = currentValues
  setTimeout(() => {
    const newValues: IndividualResultAnimated[] = simpleClone(newValue).map((result) => {
      const resultAnimated: IndividualResultAnimated = {
        ...result,
        animateThisAthlete: false,
        hasScoreChanged: false,
        hasPositionImproved: false,
        hasChangedPosition: false,
        animateNewScore: false
      }
      if (result.athleteId === athleteId) {
        resultAnimated.hasScoreChanged = false
        resultAnimated.animateNewScore = true
      }
      return resultAnimated
    })

    setInternalResults(newValues)
  }, 2000)
}

/*
function animateChange2(newValue: IndividualResult[], oldValue: IndividualResult[]): void {
  // get the athleteId of the first athlete with animateThisAthlete set to true
  const animateThisAthlete = newValue.find((result, index) => {
    return result.animateThisAthlete
  })
  if (!animateThisAthlete) {
    return
  }

  // remove classes: pulse-change-yellow, pulse-change-green, pulse-change-red from all athletes
  const athleteResults = document.querySelectorAll(
    '.pulse-change-yellow, .pulse-change-green, .pulse-change-red'
  )
  athleteResults.forEach((athleteResult) => {
    athleteResult.classList.remove('pulse-change-yellow', 'pulse-change-green', 'pulse-change-red')
  })

  const athleteId = animateThisAthlete.athleteId
  const reorderDetails = athleteReorderDetails(oldValue, newValue, athleteId)
  console.log('reorderDetails', reorderDetails)

  if (reorderDetails.hasScoreChanged) {
    // animate the score change
    const athleteResultDomElement = document.getElementById(`athlete-result-${athleteId}`)
    if (!athleteResultDomElement) {
      console.warn('A athleteResultDomElement not found by Id: ' + athleteId)
    }
    if (athleteResultDomElement) {
      athleteResultDomElement.classList.add('pulse-change-yellow')
      setTimeout(() => {
        // athleteResult.classList.remove('pulse-change-yellow')
        setInternalResults(newValue, oldValue)
        // if (reorderDetails.hasChangedPosition) {
        // animate the position change
        setTimeout(() => {
          const athleteResultDomElement = document.getElementById(`athlete-result-${athleteId}`)
          if (!athleteResultDomElement) {
            console.warn('B athleteResultDomElement not found by Id: ' + athleteId)
          }
          if (athleteResultDomElement) {
            if (reorderDetails.newPosition === reorderDetails.originalPosition) {
              return
            }
            const hasPositionImproved = reorderDetails.newPosition < reorderDetails.originalPosition
            console.warn('hasPositionImproved: ' + hasPositionImproved)
            athleteResultDomElement.classList.remove('pulse-change-yellow')
            athleteResultDomElement.classList.add(
              hasPositionImproved ? 'pulse-change-green' : 'pulse-change-red'
            )
          }
        }, 500)

        // }
      }, 2000)
    }
  }
}
*/

function setInternalResults(newValue: IndividualResultAnimated[]): void {
  // lastResults.value = simpleClone(oldValue)
  resultsInternal.value = sortRaceResultsByPosition(
    simpleClone(newValue)
  ) as IndividualResultAnimated[]
}

// const resultsSorted = computed(() => {
//   return sortRaceResultsByPosition(resultsInternal.value)
// })

function getIndividualEntryForAthlete(athleteId: number | string): IndividualEntry | undefined {
  if (!props.entries) {
    return undefined
  }
  return props.entries[athleteId.toString()]
}
</script>

<style scoped>
.race-result--position {
  width: 50px;
}

.pulse-change-green {
  animation-duration: 2s;
  animation-name: pulse-change-green;
}

.pulse-change-yellow {
  animation-duration: 2s;
  animation-name: pulse-change-yellow;
}

.pulse-change-red {
  animation-duration: 2s;
  animation-name: pulse-change-red;
}
</style>
