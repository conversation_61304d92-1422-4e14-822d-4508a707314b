<template>
  <HybridStartRace
    :show-position="true"
    :is-track="isTrack"
    :pof10="getPo10"
    :class="animateClass"
    :urn="individualEntry?.URN ? individualEntry.URN : ''"
    :title="'athlete id: ' + individualResult.athleteId"
  >
    <template #first-col>
      <span class="e4s-lite-number e4s-lite-number--position-lane">{{
        getPosition(individualResult)
      }}</span>
    </template>
    <template #position>
      <span class="e4s-lite-number e4s-lite-number--position-lane">{{
        getPosition(individualResult)
      }}</span>
    </template>
    <template #heatNo>
      <span class="e4s-lite-number">{{ heatNo }}</span>
    </template>
    <template #laneNoLabel v-if="hasOverallPosition">
      <span>Heat Pos:</span>
    </template>
    <template #laneNo>
      <span class="e4s-lite-number">{{
        hasOverallPosition ? individualResult.position : individualResult.laneNo
      }}</span>
    </template>
    <template #bibNo>
      {{ getBibNumber }}
    </template>
    <template #athlete-name>
      <span class="e4s-lite-table--standard-text">{{ individualResult.athlete }}</span>
    </template>
    <template #affiliation>
      {{ individualResult.affilation }}
    </template>
    <template #performance>
      <div class="e4s-flex-row e4s-justify-flex-row-vert-center e4s-gap--standard">
        <span class="e4s-lite-table--qualify" v-text="getQualify"></span>
        <div
          v-text="getScore"
          class="e4s-align-self-flex-end e4s-lite-number e4s-lite-number--score"
        ></div>
      </div>
    </template>
    <template #sb-perf>
      {{ getPo10SB }}
    </template>
    <template #pb-perf> {{ getPo10PB }} </template>
    <template #po10-link>
      <PowerOfTenLink :urn="getUrn" image-height="16px" />
    </template>
    <!--    <template #extra-row v-if="hasTrialMap">-->
    <!--      &lt;!&ndash;      <Collapse title="More Info"></Collapse>&ndash;&gt;-->
    <!--      <div class="e4s-flex-row e4s-flex-row&#45;&#45;end" v-if="hasTrialMap">-->
    <!--        <Trials :trails="athleteTrialMap" />-->
    <!--      </div>-->
    <!--    </template>-->
    <template #trials v-if="hasTrialMap">
      <Trials v-if="hasTrialMap" :trails="athleteTrialMap" />
    </template>
    <!--    <Trials #trials v-if="hasTrialMap" :trails="athleteTrialMap" />-->
  </HybridStartRace>
  <!--  {{ apiCompetitionAthlete }}-->
</template>

<script setup lang="ts">
import type {
  AthleteTrialMap,
  IndividualEntry,
  Pof10
} from '@/components/results/store-comp-results'
import { computed, type PropType, ref, watch } from 'vue'

import {
  getBibNo,
  getPosition,
  getScoreToDisplay,
  isTrackEvent
} from '@/components/results/results-service'
import HybridStartRace from '@/components/results/HybridStartRace.vue'
import * as ResultsService from '@/components/results/results-service'
import PowerOfTenLink from '@/common/ui/PowerOfTenLink.vue'
import Trials from '@/components/results/race-results/Trials.vue'
import type { IndividualResultAnimated } from '@/components/results/race-results/RaceResults.vue'
import { getCurrentTrial } from '@/components/results/socket/live-feed/live-feed-service'
import type { ApiCompetitionAthlete } from '@/components/results/middleware/competition-converter.models'

const props = defineProps({
  individualResult: {
    type: Object as PropType<IndividualResultAnimated>,
    required: true
  },
  apiCompetitionAthlete: {
    type: Object as PropType<ApiCompetitionAthlete | null>,
    required: true
  },
  individualEntry: {
    type: Object as PropType<IndividualEntry | undefined>,
    required: false
  },
  isTrack: {
    type: Boolean,
    required: false
  },
  isTeamEvent: {
    type: Boolean,
    required: true
  },
  heatNo: {
    type: [Number, String],
    required: true
  },
  athleteTrialMap: {
    type: Object as PropType<AthleteTrialMap>,
    default: () => {
      return {}
    }
  }
})

const animateClass = ref('')

watch(
  () => props.individualResult,
  (newvalue, oldValue) => {
    console.log('RaceResult individualResult changed', newvalue, oldValue)
    setAnimateClass(newvalue, oldValue)
  }
)
function setAnimateClass(newvalue: IndividualResultAnimated, oldValue: IndividualResultAnimated) {
  if (newvalue.hasScoreChanged) {
    animateClass.value = 'pulse-change-yellow'
    return
  } else if (newvalue.animateNewScore) {
    animateClass.value = 'pulse-change-green'
    return
  }
  animateClass.value = ''
}

// const getAnimateClass = computed(() => {
//   return {
//     'pulse-change-green': props.individualResult.hasScoreChanged,
//     'pulse-change-yellow': props.individualResult.hasChangedPosition,
//     'pulse-change-red': props.individualResult.hasPositionImproved
//   }
// })

const getPo10SB = computed(() => {
  return getPo10Value('SB')
})

const getPo10PB = computed(() => {
  return getPo10Value('PB')
})

function getPo10Value(key: 'SB' | 'PB'): string {
  const defaultPo10: Pof10 = {
    pbPerf: '',
    pbAchieved: '',
    sbPerf: '',
    sbAchieved: ''
  }
  if (!(props.individualEntry && props.individualEntry.pof10)) {
    return ''
  }
  return ResultsService.getPo10Value(props.individualEntry.pof10, key, props.isTrack)
}

const getPo10 = computed<Pof10>(() => {
  const defaultPo10: Pof10 = {
    pbPerf: '',
    pbAchieved: '',
    sbPerf: '',
    sbAchieved: ''
  }
  return props.individualEntry && props.individualEntry.pof10
    ? props.individualEntry.pof10
    : defaultPo10
})

const getUrn = computed(() => {
  return props.individualEntry?.URN
})

const hasTrialMap = computed(() => {
  return Object.keys(props.athleteTrialMap).length > 0
})

const getCurrentTrialString = computed<string>(() => {
  if (!props.individualResult.fieldResultAthlete) {
    return ''
  }
  const currentTrial = getCurrentTrial(props.individualResult.fieldResultAthlete)
  return currentTrial ? currentTrial.trialNo + ' ' + currentTrial.scoreValue : ''
})

const getQualify = computed(() => {
  return props.individualResult.qualify ? props.individualResult.qualify : ''
})

const getScore = computed(() => {
  return getScoreToDisplay(props.individualResult.score, props.isTrack) + (props.isTrack ? '' : 'm')
})

const getBibNumber = computed(() => {
  return getBibNo(props.individualEntry)
})

const hasOverallPosition = computed(() => {
  return props.individualResult.positionOverall && props.individualResult.positionOverall > 0
})
</script>

<style scoped>
.race-result--position {
  width: 50px;
}

.pulse-change-green {
  animation-duration: 2s;
  animation-name: pulse-change-green;
}

.pulse-change-yellow {
  animation-duration: 2s;
  animation-name: pulse-change-yellow;
}

.pulse-change-red {
  animation-duration: 2s;
  animation-name: pulse-change-red;
}
</style>
