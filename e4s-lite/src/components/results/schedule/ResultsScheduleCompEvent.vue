<template>
  <div class="e4s-flex-column e4s-flex-grow e4s-gap--small" :class="getCss">
    <div class="e4s-flex-row e4s-justify-flex-space-between-xxx">
      <div class="e4s-flex-column e4s-gap--small">
        <div class="e4s-flex-row e4s-justify-flex-row-vert-center">
          <!--          <div class="e4s-flex-column e4s-gap&#45;&#45;small results-schedule-comp-event&#45;&#45;type-no">-->
          <!--            <div v-text="getEventTime" class="e4s-subheader&#45;&#45;general-qazz"></div>-->
          <!--            <div v-text="compEvent.typeNo + ':'"></div>-->
          <!--          </div>-->
          <div class="e4s-flex-row results-schedule-comp-event--type-no">
            <div
              v-text="getEventTime"
              :style="hasResults ? 'font-weight: bold;' : ''"
              class="e4s-lite-number"
            ></div>
          </div>

          <div class="e4s-flex-row e4s-gap--small e4s-justify-flex-row-vert-center">
            <div
              v-text="compEvent.typeNo + ': ' + compEvent.name"
              :style="hasResults ? 'font-weight: bold;' : ''"
            ></div>
            <div class="e4s-flex-row e4s-gap--small">
              <span v-text="getEventText" class="e4s-subheader--general"></span>
            </div>
          </div>
        </div>
      </div>

      <div class="e4s-flex-column e4s-gap--small e4s-flex-row--end">
        <InputCheckboxObject
          v-if="showFollowEventSetup"
          :value="!!isFollowing"
          value-label="Follow"
          @input="shouldFollowEvent"
        />
        <div
          v-if="!showFollowEventSetup"
          class="e4s-flex-column e4s-gap--standard e4s-flex-row--end"
        >
          <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
            <!--            <PrimaryLink-->
            <!--              :link-text="'View ' + (hasResults ? 'Results' : 'Details')"-->
            <!--              @click="showDetails"-->
            <!--            />-->

            <ButtonGenericV2
              class="e4s-button--slim"
              :button-type="hasResults ? 'primary' : 'secondary'"
              :text="'View ' + (hasResults ? 'Results' : 'Details')"
              @click="showDetails"
            />

            <!--            :class="hasResults ? 'results-schedule-comp-event&#45;&#45;has-results-link' : ''"-->
          </div>

          <div
            class="e4s-flex-row e4s-gap--small e4s-flex-row--end e4s-justify-flex-row-vert-center"
          >
            <PulseIndicator
              v-if="getIsFollowing"
              color="var(--socket-active-color)"
              :animate="true"
            />
            <div v-text="(isTeam ? 'Teams' : 'Entries') + ':'"></div>
            <div v-text="entryCount"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, type PropType } from 'vue'
import type { CompEvent, ShouldFollowEvent } from '@/components/results/store-comp-results'
import * as ResultsService from '@/components/results/results-service'
import ButtonGenericV2 from '@/common/ui/buttons/button-generic-v2.vue'
import type { IBaseConcrete } from '@/common/common-models'
import TickIcon from '@/common/ui/svg/TickIcon.vue'
import InputCheckboxObject from '@/common/ui/fields/InputCheckboxObject.vue'
import PrimaryLink from '@/common/ui/href/PrimaryLink.vue'
import PulseIndicator from '@/common/ui/icons/PulseIndicator.vue'

const props = defineProps({
  compEvent: {
    type: Object as PropType<CompEvent>,
    required: true
  },
  isFollowing: {
    type: Object as PropType<IBaseConcrete | undefined>,
    required: false,
    default: () => undefined
  },
  showFollowEventSetup: {
    type: Boolean,
    default: false
  }
})

//  define emits.
const emit = defineEmits<{
  (e: 'showDetails', value: CompEvent): void
  (e: 'shouldFollowEvent', value: ShouldFollowEvent): void
}>()

const isTeam = computed(() => {
  return ResultsService.isTeamEvent(props.compEvent)
})

const getEventTime = computed(() => {
  return ResultsService.getCompEventStartTime(props.compEvent)
})

const entryCount = computed(() => {
  return isTeam.value
    ? ResultsService.howManyTeams(props.compEvent)
    : ResultsService.compEventHasHowManyEntries(props.compEvent)
})

const resultCount = computed(() => {
  return ResultsService.compEventHasHowManyResultHeats(props.compEvent)
})

const getEventText = computed(() => {
  return props.compEvent.eventText ? props.compEvent.eventText : ''
})

const hasResults = computed(() => {
  return resultCount.value > 0
  // return entryCount.value > 0
})

function showDetails() {
  emit('showDetails', props.compEvent)
}

function shouldFollowEvent() {
  const currentlyFollowing = props.isFollowing
  emit('shouldFollowEvent', {
    isFollowing: !currentlyFollowing,
    compEvent: props.compEvent
  })
}

const getIsFollowing = computed(() => {
  return props.isFollowing
})

const getCss = computed(() => {
  const css = []

  if (resultCount.value > 0) {
    css.push('results-schedule-comp-event--has-results')
  }

  if (props.isFollowing) {
    css.push('results-schedule-comp-event--is-following')
  }

  return css
})
</script>

<style scoped>
.results-schedule-comp-event--event-time {
  width: 50px;
}
.results-schedule-comp-event--type-no {
  width: 50px;
}

.results-schedule-comp-event--has-results-x {
  background: var(--green-200);
}

.results-schedule-comp-event--has-results-link {
  color: var(--green-700);
}

.results-schedule-comp-event--is-following-x {
  background: var(--green-100);
  border-left: 3px solid var(--green-500);
}
</style>
