<template>
  <div class="e4s-flex-column e4s-sticky-navigation" id="e4s-nav-bar">
    <div
      class="e4s-navigation-bar e4s-navigation-bar--primary e4s-justify-flex-center"
      :class="configController.getCssEnvSuffix.value"
    >
      <!--      <div class="e4s-navigation-bar&#45;&#45;content-wrapper" :class="getContentWidth">-->
      <div
        class="e4s-flex-row e4s-gap--large e4s-justify-flex-row-vert-center"
        :class="getContentWidth"
      >
        <!-- Logo -->
        <div class="e4s-navigation-bar-logo--container">
          <a href="#" v-on:click.prevent="goHome">
            <E4sLogoSvg height="45px" />
          </a>
        </div>

        <span
          class="e4s-flex-row--end e4s-navigation-bar-logo--title text-truncate"
          v-text="storeCompResults.state.competitionOnTheDay.name"
        ></span>

        <!-- Auth Status -->
        <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
          <div v-if="auth.isAuthenticated.value" class="e4s-flex-row e4s-gap--small e4s-justify-flex-row-vert-center">
            <span class="e4s-body--200">{{ auth.user.value?.name }}</span>
            <ButtonGenericV2
              button-type="secondary"
              text="Logout"
              class="e4s-button--75"
              @click="handleLogout"
            />
          </div>
          <div v-else>
            <ButtonGenericV2
              button-type="primary"
              text="Login"
              class="e4s-button--75"
              @click="goToLogin"
            />
          </div>
        </div>
        <!-- /Auth Status -->
        <!-- /Logo -->
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import { useConfigController } from '@/config/useConfigStore'
import { useRouter } from 'vue-router'
import E4sLogoSvg from '@/common/ui/svg/E4sLogoSvg.vue'
import { useStoreCompResults } from '@/components/results/store-comp-results'
import { useAuth } from '@/composables/useAuth'
import ButtonGenericV2 from '@/common/ui/buttons/button-generic-v2.vue'

const configController = useConfigController()
const router = useRouter()
const auth = useAuth()

const storeCompResults = useStoreCompResults()

const getContentWidth = computed(() => {
  // const routeName: LaunchRouteValueV2 = route.name
  //   ? route.name
  //   : ("" as any as LaunchRouteValueV2);
  // if (widerRoutesClass[routeName]) {
  //   return widerRoutesClass[routeName];
  // }
  return 'e4s-width-controller'
})

function goHome() {
  console.log('TheHeader.goHome()')
  router.push({ name: 'home' })
}

function goToLogin() {
  router.push({ name: 'login' })
}

function handleLogout() {
  auth.logout()
  router.push({ name: 'home' })
}
</script>
<style scoped>
.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  max-width: 100%;
  display: block;
}
</style>
