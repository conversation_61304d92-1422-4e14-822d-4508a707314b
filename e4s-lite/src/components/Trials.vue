<template>
  <div class="e4s-flex-row e4s-gap--standard e4s-subheader--general e4s-subheader--300">
    <div v-for="(trial, key) in trails" :key="trial" class="e4s-flex-row">
      <div v-text="key + ':'" class="trials--key"></div>
      <div v-text="cleanUpScoreValueForDisplay(trial!)" class="trials--value"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
import type { AthleteTrialMap } from '@/components/results/store-comp-results'
import type { PropType } from 'vue'
import { cleanUpScoreValueForDisplay } from '@/components/results/results-service'

const props = defineProps({
  trails: {
    type: Object as PropType<AthleteTrialMap>,
    required: true
  }
})
</script>

<style scoped>
.trials--key {
  width: 15px;
}
.trials--value {
}
</style>
