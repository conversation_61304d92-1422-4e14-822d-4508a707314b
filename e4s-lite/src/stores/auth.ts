import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { useMyFetch } from '@/services/useMyFetch'
import type { IServerResponse } from '@/common/common-models'
import type { USER_ROLES } from '@/config/config-app-models'

// Types
export interface AuthUser {
  id: number
  name: string
  email: string
  role: USER_ROLES
  permissions?: string[]
}

export interface AuthVerifyResponse {
  user: AuthUser
}

export interface AuthState {
  user: AuthUser | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<AuthUser | null>(null)
  const token = ref<string | null>(localStorage.getItem('auth_token'))
  const isLoading = ref<boolean>(false)
  const error = ref<string | null>(null)

  // Getters
  const isAuthenticated = computed(() => !!user.value && !!token.value)
  const userRole = computed(() => user.value?.role || null)
  const hasPermission = computed(() => (permission: string) => {
    return user.value?.permissions?.includes(permission) || false
  })

  // Actions
  const setToken = (newToken: string | null): void => {
    token.value = newToken
    if (newToken) {
      localStorage.setItem('auth_token', newToken)
    } else {
      localStorage.removeItem('auth_token')
    }
  }

  const setUser = (newUser: AuthUser | null): void => {
    user.value = newUser
  }

  const setError = (newError: string | null): void => {
    error.value = newError
  }

  const setLoading = (loading: boolean): void => {
    isLoading.value = loading
  }

  const login = async (redirectUrl?: string): Promise<void> => {
    const API_BASE_URL = import.meta.env.VITE_APP_API_HOST || 'http://localhost:3000'

    // Construct the success callback URL that will handle the token
    const currentOrigin = window.location.origin
    const successUrl = `${currentOrigin}/#/auth-success`

    // Add redirect parameter if provided
    const params = new URLSearchParams()
    params.append('successUrl', successUrl)
    if (redirectUrl) {
      params.append('redirect', redirectUrl)
    }

    window.location.href = `${API_BASE_URL}/api/auth/google?${params.toString()}`
  }

  const verifyToken = async (): Promise<boolean> => {
    if (!token.value) {
      return false
    }

    setLoading(true)
    setError(null)

    try {
      const response = await useMyFetch(`/api/auth/verify`, {
        headers: {
          Authorization: `Bearer ${token.value}`
        }
      }).get().json<IServerResponse<AuthVerifyResponse>>()

      // Wait for the response data
      const data = await response.data.value

      if (data?.errNo === 0 && data?.data?.user) {
        setUser(data.data.user)
        setError(null)
        return true
      } else {
        throw new Error(data?.error || 'Authentication failed')
      }
    } catch (err) {
      console.error('Token verification failed:', err)
      setError('Authentication failed. Please try again.')
      logout()
      return false
    } finally {
      setLoading(false)
    }
  }

  const logout = (): void => {
    setUser(null)
    setToken(null)
    setError(null)
  }

  const handleTokenFromUrl = (): void => {
    // This method is now primarily handled by the AuthSuccess component
    // but we keep it for backward compatibility or direct token handling
    const urlParams = new URLSearchParams(window.location.search)
    const tokenFromUrl = urlParams.get('token')

    if (tokenFromUrl) {
      setToken(tokenFromUrl)
      // Clean URL
      window.history.replaceState({}, document.title, window.location.pathname)
      // Verify the token
      verifyToken()
    }
  }

  const initialize = async (): Promise<void> => {
    // Check for token in URL first (OAuth redirect)
    handleTokenFromUrl()
    
    // If we have a token in storage, verify it
    if (token.value && !user.value) {
      await verifyToken()
    }
  }

  return {
    // State
    user,
    token,
    isLoading,
    error,
    // Getters
    isAuthenticated,
    userRole,
    hasPermission,
    // Actions
    setToken,
    setUser,
    setError,
    setLoading,
    login,
    verifyToken,
    logout,
    handleTokenFromUrl,
    initialize
  }
})
