<template>
  <div class="pulse-indicator" :style="getStyles"></div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  size: {
    type: String,
    default: '10px'
  },
  color: {
    type: String,
    default: 'var(--e4s-status-pill--success__background)'
  },
  borderColor: {
    type: String,
    default: 'var(--e4s-status-pill--success__border-color)'
  },
  animate: {
    type: Boolean,
    default: true
  }
})

const getStyles = computed(() => {
  return {
    height: props.size,
    width: props.size,
    background: props.color,
    borderColor: props.borderColor,
    animation: props.animate ? 'pulse-present 2s infinite' : 'none'
  }
})
</script>

<style>
.pulse-indicator {
  border-radius: 50%;
  border: 1px solid var(--e4s-status-pill--success__border-color);
  display: inline-block;
  z-index: 101;
  position: relative;
}

@keyframes pulse-present {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
</style>
