<template>
  <label class="input-checkbox-v2--label">
    <input
      type="checkbox"
      class="browser-default e4s-input-field input-checkbox-v2--input"
      style="accent-color: var(--e4s-button--primary__background)"
      :disabled="isDisabled"
      v-model="valueInternal"
      v-on:change="submit"
    />
    <!--Leave this as div for now, else have a fight with Materialize-->
    <slot
      ><div
        class="input-checkbox-v2--label"
        :class="isDisabled ? 'input-checkbox-v2--label-disabled' : ''"
        v-text="valueLabel"
        v-if="valueLabel.length > 0"
      ></div
    ></slot>
  </label>
</template>

<script lang="ts">
// import { defineComponent, ref, SetupContext, watch } from '@vue/composition-api'
import { defineComponent, type PropType, ref, type SetupContext, watch } from 'vue'

export default defineComponent({
  name: 'input-checkbox-v2',
  components: {},
  inheritAttrs: false,
  props: {
    value: {
      type: Boolean,
      required: true
    },
    valueLabel: {
      type: String,
      default: ''
    },
    checkBoxClass: {
      type: String,
      default: () => {
        return ''
      }
    },
    isDisabled: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    isDebug: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  setup(
    props: {
      value: boolean
      valueLabel: string
      checkBoxClass: string
      isDisabled: boolean
      isDebug: boolean
    },
    context: SetupContext
  ) {
    const valueInternal = ref(props.value)

    watch(
      () => props.value,
      (newValue: boolean) => {
        if (props.isDebug) {
          console.log(
            'input-checkbox-v2.watch() props.value: ' + props.value + ', changing to: ' + newValue
          )
        }
        valueInternal.value = newValue
        // if (newValue !== valueInternal.value) {
        //   valueInternal.value = newValue;
        // }
      },
      {
        immediate: true
      }
    )

    function submit() {
      console.log(
        'form-generic-input-text-v2.submit() value: ' +
          props.value +
          ', changing to: ' +
          !props.value
      )
      // context.emit("input", !props.value);
      context.emit('input', valueInternal.value)
      context.emit('onChange', valueInternal.value)
      // console.log(">>>>>>>>>>", attrs.value);
    }
    // function keypressEnter() {
    //   console.log(
    //     "form-generic-input-text-v2.keypressEnter() value: " +
    //       valueInternal.value
    //   );
    //   context.emit("keypressEnter", valueInternal.value);
    // }
    //
    // function everything(x: unknown) {
    //   console.log("everything");
    // }

    return { valueInternal, submit }
  }
})
</script>

<style>
/*  We need to override Materialize!!!  */
/*.input-checkbox-v2--label {*/
/*  display: flex;*/
/*  justify-content: center;*/
/*  align-items: center;*/
/*  vertical-align: middle;*/
/*  word-wrap: break-word;*/
/*  color: black;*/
/*  gap: var(--e4s-gap--standard);*/
/*}*/

/*.input-checkbox-v2--input {*/
/*  width: 16px !important;*/
/*}*/

/*.input-checkbox-v2--input:not(:checked),*/
/*.input-checkbox-v2--input:checked {*/
/*  position: unset !important;*/
/*  opacity: 1 !important;*/
/*  pointer-events: auto !important;*/
/*}*/
</style>
