import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import type { USER_ROLES } from '@/config/config-app-models'

/**
 * Composable for authentication functionality
 * Provides easy access to auth state and methods
 */
export const useAuth = () => {
  const authStore = useAuthStore()

  // Reactive state
  const user = computed(() => authStore.user)
  const isAuthenticated = computed(() => authStore.isAuthenticated)
  const isLoading = computed(() => authStore.isLoading)
  const error = computed(() => authStore.error)
  const userRole = computed(() => authStore.userRole)

  // Helper methods
  const hasRole = (role: USER_ROLES): boolean => {
    return authStore.userRole === role
  }

  const hasAnyRole = (roles: USER_ROLES[]): boolean => {
    return authStore.userRole ? roles.includes(authStore.userRole) : false
  }

  const hasPermission = (permission: string): boolean => {
    return authStore.hasPermission(permission)
  }

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => authStore.hasPermission(permission))
  }

  const isAdmin = computed(() => {
    return hasAnyRole(['APPADMIN', 'E4SUSER'])
  })

  const canAccessAdmin = computed(() => {
    return isAuthenticated.value && isAdmin.value
  })

  // Auth actions
  const login = (redirectUrl?: string) => {
    return authStore.login(redirectUrl)
  }

  const logout = () => {
    authStore.logout()
  }

  const verifyToken = () => {
    return authStore.verifyToken()
  }

  return {
    // State
    user,
    isAuthenticated,
    isLoading,
    error,
    userRole,
    // Computed helpers
    isAdmin,
    canAccessAdmin,
    // Methods
    hasRole,
    hasAnyRole,
    hasPermission,
    hasAnyPermission,
    login,
    logout,
    verifyToken
  }
}
