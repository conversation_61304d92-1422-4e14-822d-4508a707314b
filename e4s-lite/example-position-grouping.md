# Position Grouping Example

## Function: `getOverallResultsWithPositions`

This function takes results from `getOverallResults` and assigns positions based on score grouping. Athletes with identical scores receive the same position.

### Track Event Example (Lower scores are better)

**Input scores:** 9.80, 10.50, 10.50, 11.20, 11.20, 11.20, "", "DNS"

**Output positions:**
- 9.80 → Position 1 (1st place)
- 10.50 → Position 2 (tied for 2nd)
- 10.50 → Position 2 (tied for 2nd)
- 11.20 → Position 4 (tied for 4th, after 2 people in 2nd)
- 11.20 → Position 4 (tied for 4th)
- 11.20 → Position 4 (tied for 4th)
- "" → Position 0 (invalid score)
- "DNS" → Position 0 (invalid score)

### Field Event Example (Higher scores are better)

**Input scores:** 7.80, 7.80, 6.20, 5.50, "0"

**Output positions:**
- 7.80 → Position 1 (tied for 1st)
- 7.80 → Position 1 (tied for 1st)
- 6.20 → Position 3 (3rd place, after 2 people tied for 1st)
- 5.50 → Position 4 (4th place)
- "0" → Position 0 (invalid score)

### Key Features

1. **Proper position calculation**: When athletes tie, the next position accounts for the number of tied athletes
2. **Invalid score handling**: Empty, zero, or non-numeric scores get position 0 and are sorted to the bottom
3. **Event type awareness**: Automatically handles track vs field event sorting (ascending vs descending)
4. **Stable sorting**: Maintains original order for athletes with identical scores

### Usage

```typescript
import { getOverallResultsWithPositions } from '@/components/results/results-service'

const resultsWithPositions = getOverallResultsWithPositions(compEvent)

// Each result now has the correct position based on score grouping AND heat information
resultsWithPositions.forEach(result => {
  console.log(`${result.athlete}: ${result.score} (Position: ${result.position}, Heat: ${result.heatNo})`)
})
```

### Heat Information Example

**Input data:**
- Heat 1: Athlete A (10.50), Athlete B (11.20)
- Heat 2: Athlete C (9.80), Athlete D (10.50)
- Heat 3: Athlete E (invalid score)

**Output with positions and heat info:**
- Athlete C: 9.80 (Position: 1, Heat: 2)
- Athlete A: 10.50 (Position: 2, Heat: 1) - tied for 2nd
- Athlete D: 10.50 (Position: 2, Heat: 2) - tied for 2nd
- Athlete B: 11.20 (Position: 4, Heat: 1)
- Athlete E: "" (Position: 0, Heat: 3) - invalid score

### Key Benefits

1. **Preserves heat context**: You can still see which heat each athlete competed in
2. **Correct position calculation**: Athletes with identical scores get the same position
3. **Ready for display**: Results are sorted and positioned correctly for leaderboards
4. **Backward compatible**: Still works with existing code that doesn't need heat info
